package net.summerfarm.wnc.inbound.scheduler.customFence;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.HistoryOutOfCustomFenceService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:自定义围栏POI匹配任务
 * date: 2023/8/30 11:21
 */
@Slf4j
@Component
public class CustomFencePoiHandleJob extends XianMuJavaProcessorV2 {

    @Resource
    private HistoryOutOfCustomFenceService customFenceService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("自定义围栏POI匹配任务");
        //查询总的条数数据
        Long contactNum = customFenceService.queryContactTotal();
        if(contactNum == null){
            return new ProcessResult(true);
        }
        long num = contactNum/500 + 1;
        for (int i = 0; i < num; i++) {
            customFenceService.compareContactPoi(i*500, (i+1)*500);
        }
        return new ProcessResult(true);
    }
}
