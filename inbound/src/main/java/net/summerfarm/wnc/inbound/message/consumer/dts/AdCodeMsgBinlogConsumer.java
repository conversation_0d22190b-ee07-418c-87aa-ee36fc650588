package net.summerfarm.wnc.inbound.message.consumer.dts;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.HistoryOutOfCustomFenceService;
import net.summerfarm.wnc.common.commoni.DtsModel;
import net.summerfarm.wnc.common.commoni.DtsModelTypeEnum;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/12/6 11:50<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = "mysql-binlog",
        tag = "ad_code_msg",
        consumerGroup = "GID_wnc_binlog",
        maxReconsumeTimes = 5)
public class AdCodeMsgBinlogConsumer extends AbstractMqListener<DtsModel> {
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private HistoryOutOfCustomFenceService customFenceService;

    @Override
    public void process(DtsModel dtsModel) {
        log.info("AdCodeMsgBinlogConsumer收到消息，{}", JSON.toJSONString(dtsModel));
        /*DtsModelTypeEnum dtsModelTypeEnum = DtsModelTypeEnum.getDtsModelTypeByType(dtsModel.getType());
        if (dtsModelTypeEnum == null) {
            log.info("非法DML无法解析,msgKey：{},丢弃消息", dtsModel.getMsgKey());
            return;
        }
        dtsModel.setDtsModelTypeEnum(dtsModelTypeEnum);
        //批量DML操作解析
        List<DtsModel> dtsModels = dtsModel.convertModelList();
        for (DtsModel model : dtsModels) {
            StringJoiner key = new StringJoiner(":");
            //获取ad_code
            key.add("wnc").add("ad_code_msg").add("adCode").add(dtsModel.getData().get(0).get("ad_code"));
            RLock redissonLock = redissonClient.getLock(key.toString());
            try {
                if (!redissonLock.tryLock(30L, 5L, TimeUnit.SECONDS)) {
                    throw new BizException("ad_code_msg数据同步,消息重试处理");
                }
                this.processModel(model);
            } catch (InterruptedException e) {
                throw new BizException("ad_code_msg数据同步异常中断,消息重试处理");
            } finally {
                if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                    redissonLock.unlock();
                }
            }
        }*/
    }

    /**
     * 操作处理
     * @param dtsModel 操作对象
     */
    private void processModel(DtsModel dtsModel) {
        if (dtsModel.getDtsModelTypeEnum() == DtsModelTypeEnum.INSERT) {
            Map<String, String> dataMap = dtsModel.getData().get(0);
            String adCode = dataMap.get("ad_code");
            if (StringUtils.isBlank(adCode)) {
                return;
            }
            String province = dataMap.get("province");
            String city = dataMap.get("city");
            String area = dataMap.get("area");

            //初始化信息
            customFenceService.adCodeInfo2Es(adCode, province, city, area);
        } else {
            log.info("无需处理的DML操作：{},msgKey：{},丢弃消息", dtsModel.getType(), dtsModel.getMsgKey());
        }
    }
}
