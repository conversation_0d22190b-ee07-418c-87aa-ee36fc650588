package net.summerfarm.wnc.application.service.changeTask;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.service.FenceService;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.FenceDomainService;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.CustomFenceAreaEsCommandDomainService;
import net.summerfarm.wnc.domain.fence.service.WncFenceAreaChangeRecordsCommandDomainService;
import net.summerfarm.wnc.domain.fence.service.WncFenceChangeRecordsCommandDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 围栏切仓任务处理服务
 * date: 2025/9/2 11:38<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class FenceChangTaskHandleService {

    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private WncFenceChangeRecordsCommandDomainService wncFenceChangeRecordsCommandDomainService;
    @Resource
    private FenceService fenceService;
    @Autowired
    private FenceDomainService fenceDomainService;
    @Autowired
    private CustomFenceAreaEsCommandDomainService customFenceAreaEsCommandDomainService;
    @Resource
    private WncFenceAreaChangeRecordsCommandDomainService wncFenceAreaChangeRecordsCommandDomainService;

    /**
     * 执行围栏切仓区域处理任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeFenceChangeAreaHandle(WncCityAreaChangeWarehouseRecordsEnums.AreaDefinationType areaDefinationType) {
        if(areaDefinationType == null){
            log.info("围栏切仓区域处理任务 areaDefinationType is null 已结束");
            return;
        }
        log.info("围栏切仓区域处理任务，区域定义类型：{}", areaDefinationType.getValue());

        // 获取当天已到执行时间、状态为等待生效的城市区域记录
        LocalDateTime now = LocalDateTime.now();
        Integer changeStatus = WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue();
        List<WncCityAreaChangeWarehouseRecordsEntity> executableRecords = wncCityAreaChangeWarehouseRecordsQueryRepository.selectExecutableRecords(now, changeStatus);
        if (CollectionUtils.isEmpty(executableRecords)) {
            log.info("围栏切仓区域处理任务，无可执行切仓任务");
            return;
        }

        Map<String, List<WncCityAreaChangeWarehouseRecordsEntity>> changeBatchNoMap = executableRecords.stream().collect(Collectors.groupingBy(WncCityAreaChangeWarehouseRecordsEntity::getChangeBatchNo));

        changeBatchNoMap.forEach((changeBatchNo, records) -> {
            // 查询切仓批次快照前后信息
            List<WncFenceChangeRecordsEntity> fenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo);
            if (CollectionUtils.isEmpty(fenceChangeRecords)) {
                return;
            }
            // 根据记录信息创建或者更新围栏、区域数据，只处理变更后的快照数据就好了
            List<WncFenceChangeRecordsEntity> afterFenceChangeRecords = fenceChangeRecords.stream()
                    .filter(fenceRecords -> fenceRecords.getFenceChangeStage().equals(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(afterFenceChangeRecords)) {
                log.info("围栏切仓区域处理任务，无可执行围栏变更任务，changeBatchNo:{}",changeBatchNo);
                return;
            }

            // 变更前围栏快照信息
            List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords = fenceChangeRecords.stream()
                    .filter(fenceRecords -> fenceRecords.getFenceChangeStage().equals(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue()))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(beforeFenceChangeRecords)) {
                List<FenceEntity> fenceEntityList = beforeFenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getFenceDetailEntity).collect(Collectors.toList());
                List<FenceEnums.Type> types = fenceEntityList.stream().map(FenceEntity::getType).distinct().collect(Collectors.toList());
                if (types.size() == 1 && types.contains(FenceEnums.Type.CUSTOM)) {
                    // 自定义围栏 ---> 自定义围栏
                    // 1.没有围栏ID数据的创建、并回写围栏id到快照记录表中
                    this.createFenceForNoFenceIdRecordsWithWriteBackFenceId(afterFenceChangeRecords);

                    // 2.历史围栏区域解绑并设置为失效
                    this.unbindAndInvalidArea(beforeFenceChangeRecords);

                    // 3.重新生成新区域、回写fenceId、adCodeMsgId到快照记录表中
                    this.createNewAreaWithWriteBackId(afterFenceChangeRecords);

                    // 4.区域geo-shape需要保存到es里面
                    List<Long> fenceChangeRecordIds = afterFenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getId).collect(Collectors.toList());
                    customFenceAreaEsCommandDomainService.createCustomFenceArea(fenceChangeRecordIds);

                } else if (types.size() > 1 && types.contains(FenceEnums.Type.CUSTOM)) {
                    // 普通围栏 ---> 自定义

                } else if (!types.contains(FenceEnums.Type.CUSTOM)) {
                    // 普通围栏 ---> 普通围栏

                } else {
                    log.info("围栏切仓区域处理任务，围栏类型异常，changeBatchNo:{}",changeBatchNo);
                }
            } else {
                // 无 ---> 自定义
            }


            // 变更后区域数据
            List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecords = afterFenceChangeRecords.stream()
                    .map(WncFenceChangeRecordsEntity::getAreaChangeRecords)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());


            // 有围栏ID的数据进行更新
            List<WncFenceChangeRecordsEntity> hasFenceIdRecords = afterFenceChangeRecords.stream()
                    .filter(fenceRecords -> fenceRecords.getFenceId() != null)
                    .collect(Collectors.toList());

            // 有区域ID的数据，从新建围栏类型-->自定义围栏，其他区域进行解绑并失效，自定义->自定义 直接失效即可
            hasFenceIdRecords.forEach(hasFenceIdChangeRecord -> {

            });


            // 处理围栏切仓任务
            wncFenceChangeRecordsCommandDomainService.executeFenceChangeAreaHandle(changeBatchNo);
        });

        // 将切仓任务状态更新为区域切换中
        List<Long> fenceChangeTaskIds = executableRecords.stream().map(WncCityAreaChangeWarehouseRecordsEntity::getFenceChangeTaskId).distinct().collect(Collectors.toList());
        // 查询【切仓任务ID】【待处理】切仓任务
        List<FenceChangeTaskEntity> waitHandleTasks = fenceChangeTaskRepository.queryByIdsWithStatus(fenceChangeTaskIds,FenceChangeTaskEnums.Status.WAIT.getValue());
        
        waitHandleTasks.forEach(task -> {
            task.execute(FenceChangeTaskEnums.Status.AREA_CHANGE_ING);
            fenceChangeTaskRepository.update(task);
        });

        //

        // 更新城市区域切仓记录状态为正常生效中

        // 执行切仓任务
    }

    /**
     * 创建新区域并回写ID
     * @param afterFenceChangeRecords 变更后围栏记录
     */
    private void createNewAreaWithWriteBackId(List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {
        if (CollectionUtils.isEmpty(afterFenceChangeRecords)) {
            log.info("围栏切仓区域处理任务，无可执行创建区域任务");
            return;
        }
        afterFenceChangeRecords.forEach(afterFenceChangeRecord -> {
            List<WncFenceAreaChangeRecordsEntity> areaChangeRecords = afterFenceChangeRecord.getAreaChangeRecords();
            if (CollectionUtils.isEmpty(areaChangeRecords)) {
                return;
            }
            Integer fenceId = afterFenceChangeRecord.getFenceId();

            List<AdCodeMsgEntity> adCodeMsgEntities = areaChangeRecords.stream().map(e -> {
                AdCodeMsgEntity adCodeMsgDetail = e.getAdCodeMsgDetailEntity();

                AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
                adCodeMsgEntity.setAdCode(adCodeMsgDetail.getAdCode());
                adCodeMsgEntity.setProvince(adCodeMsgDetail.getProvince());
                adCodeMsgEntity.setCity(e.getCity());
                adCodeMsgEntity.setArea(e.getArea());
                adCodeMsgEntity.setLevel(adCodeMsgDetail.getLevel());
                adCodeMsgEntity.setGdId(adCodeMsgDetail.getGdId());
                adCodeMsgEntity.setStatus(adCodeMsgDetail.getStatus());
                adCodeMsgEntity.setFenceId(fenceId);
                adCodeMsgEntity.setCustomAreaName(e.getCustomAreaName());
                adCodeMsgEntity.setAreaType(AdCodeMsgEnums.AreaType.CUSTOM_AREA.getValue());
                adCodeMsgEntity.setAreaDrawType(e.getAreaDrawType());
                adCodeMsgEntity.setFenceAreaChangeId(e.getId());

                return adCodeMsgEntity;
            }).collect(Collectors.toList());

            // 生成区域并绑定围栏id
            fenceDomainService.createAreaBindingFence(adCodeMsgEntities);

            // 回写adCodeId、fenceId到快照表
            List<WncFenceAreaChangeRecordsCommandParam> params = areaChangeRecords.stream().map(e -> {
                WncFenceAreaChangeRecordsCommandParam param = new WncFenceAreaChangeRecordsCommandParam();
                param.setId(e.getId());
                param.setFenceId(e.getFenceId());
                param.setAdCodeMsgId(e.getAdCodeMsgId());
                return param;
            }).collect(Collectors.toList());

            // 更新围栏区域变更记录的围栏ID和ACM区域ID
            wncFenceAreaChangeRecordsCommandDomainService.updateAdCodeIdAndFenceId(params);
        });
    }

    private void unbindAndInvalidArea(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords) {
        if (CollectionUtils.isEmpty(beforeFenceChangeRecords)) {
            log.info("围栏切仓区域处理任务，无可执行解绑任务");
            return;
        }
        List<Integer> fenceIds = beforeFenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getFenceId).collect(Collectors.toList());
        fenceDomainService.unbindAndInvalidArea(fenceIds);
    }

    /**
     * 创建没有围栏ID的记录并回写围栏id
     * @param afterFenceChangeRecords 变更后围栏记录
     */
    private void createFenceForNoFenceIdRecordsWithWriteBackFenceId(List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {
        List<WncFenceChangeRecordsEntity> noFenceIdRecords = afterFenceChangeRecords.stream()
                .filter(fenceRecords -> fenceRecords.getFenceId() == null)
                .collect(Collectors.toList());

        // 没有围栏ID的数据进行创建
        noFenceIdRecords.forEach(noFenceIdChangeRecord -> {
            FenceEntity fenceDetailEntity = noFenceIdChangeRecord.getFenceDetailEntity();
            // 创建围栏
            fenceDomainService.createFence(fenceDetailEntity);

            // 更新变更上面的围栏信息
            WncFenceChangeRecordsCommandParam param = new WncFenceChangeRecordsCommandParam();
            param.setId(noFenceIdChangeRecord.getId());
            param.setFenceId(fenceDetailEntity.getId());
            wncFenceChangeRecordsCommandDomainService.update(param);

            noFenceIdChangeRecord.setFenceId(fenceDetailEntity.getId());
        });

    }
}
