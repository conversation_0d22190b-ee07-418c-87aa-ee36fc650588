package net.summerfarm.wnc.application.inbound.controller.fence.converter;

import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaFenceAreaVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaFenceAreaDetailVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomFenceDetailVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomAreaDetail;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义围栏转换器
 * date: 2025/8/29<br/>
 *
 * <AUTHOR> />
 */
public class CustomFenceConverter {

    /**
     * 转换Entity为CustomCityAreaVO
     */
    public static CustomCityAreaVO convertToCustomCityAreaVO(WncCityAreaChangeWarehouseRecordsEntity entity) {
        if (entity == null) {
            return null;
        }

        CustomCityAreaVO vo = new CustomCityAreaVO();
        vo.setCityAreaChangeWarehouseRecordId(entity.getId());
        vo.setProvince(entity.getProvince());
        vo.setCity(entity.getCity());
        vo.setArea(entity.getArea());
        vo.setCityAreaChangeStatus(entity.getChangeStatus());
        vo.setPreExeTime(entity.getPreExeTime());
        vo.setFenceChangeBatchNo(entity.getChangeBatchNo());
        vo.setFenceChangeTaskId(entity.getFenceChangeTaskId());
        vo.setEffectiveTime(entity.getEffectiveTime());
        vo.setOverTime(entity.getOverTime());
        vo.setAreaDefinationType(entity.getAreaDefinationType());
        return vo;
    }

    /**
     * 转换为CustomFenceAreaVO
     */
    public static CustomCityAreaFenceAreaVO convertToCustomFenceAreaVO(WncFenceChangeRecordsEntity fenceChangeRecord,
                                                                       WncFenceAreaChangeRecordsEntity areaChangeRecord) {
        if (fenceChangeRecord == null || areaChangeRecord == null) {
            return null;
        }
        
        CustomCityAreaFenceAreaVO vo = new CustomCityAreaFenceAreaVO();
        vo.setFenceName(fenceChangeRecord.getFenceName());
        vo.setCustomAreaName(areaChangeRecord.getCustomAreaName());
        vo.setGeoShape(areaChangeRecord.getGeoShape());
        vo.setFenceStatus(fenceChangeRecord.getFenceChangeStage());
        vo.setFenceStoreNo(fenceChangeRecord.getFenceStoreNo());
        vo.setFenceAreaNo(fenceChangeRecord.getFenceAreaNo());
        vo.setFenceStoreName(fenceChangeRecord.getFenceStoreName());
        vo.setFenceAreaName(fenceChangeRecord.getFenceAreaName());
        return vo;
    }

    /**
     * 转换为CustomFenceDetailVO
     */
    public static CustomFenceDetailVO convertToCustomFenceDetailVO(WncFenceChangeRecordsEntity fenceChangeRecord,
                                                                  List<CustomAreaDetail> customAreaDetails) {
        if (fenceChangeRecord == null) {
            return null;
        }
        
        CustomFenceDetailVO fenceDetailVO = new CustomFenceDetailVO();
        fenceDetailVO.setFenceChangeRecordsId(fenceChangeRecord.getId());
        fenceDetailVO.setFenceId(fenceChangeRecord.getFenceId().intValue());
        fenceDetailVO.setFenceName(fenceChangeRecord.getFenceName());
        fenceDetailVO.setFenceStatus(fenceChangeRecord.getFenceChangeStage());
        fenceDetailVO.setStoreName(fenceChangeRecord.getFenceStoreName());
        fenceDetailVO.setAreaName(fenceChangeRecord.getFenceAreaName());
        fenceDetailVO.setCustomAreaDetails(customAreaDetails != null ? customAreaDetails : new ArrayList<>());
        
        return fenceDetailVO;
    }

    /**
     * 转换为CustomAreaDetail
     */
    public static CustomAreaDetail convertToCustomAreaDetail(WncFenceAreaChangeRecordsEntity areaChangeRecord) {
        if (areaChangeRecord == null) {
            return null;
        }
        
        CustomAreaDetail areaDetail = new CustomAreaDetail();
        areaDetail.setFenceAreaChangeRecordsId(areaChangeRecord.getId());
        areaDetail.setAdCodeMsgId(areaChangeRecord.getAdCodeMsgId().intValue());
        areaDetail.setCustomAreaName(areaChangeRecord.getCustomAreaName());
        areaDetail.setAdCodeMsgStatus(areaChangeRecord.getAdCodeMsgDetailEntity() != null ? 
                                     areaChangeRecord.getAdCodeMsgDetailEntity().getStatus() : null);
        areaDetail.setGeoShape(areaChangeRecord.getGeoShape());
        
        return areaDetail;
    }

    /**
     * 转换为CustomCityAreaDetailVO
     */
    public static CustomCityAreaFenceAreaDetailVO convertToCustomCityAreaDetailVO(WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord,
                                                                                  List<CustomFenceDetailVO> customFenceDetails) {
        if (cityAreaRecord == null) {
            return null;
        }
        
        CustomCityAreaFenceAreaDetailVO result = new CustomCityAreaFenceAreaDetailVO();
        result.setCityAreaChangeWarehouseRecordId(cityAreaRecord.getId());
        result.setProvince(cityAreaRecord.getProvince());
        result.setCity(cityAreaRecord.getCity());
        result.setArea(cityAreaRecord.getArea());
        result.setCustomFenceDetails(customFenceDetails != null ? customFenceDetails : new ArrayList<>());
        
        return result;
    }

    /**
     * 批量转换Entity列表为DTO列表
     */
    public static List<CustomCityAreaVO> convertToCustomCityAreaVOList(List<WncCityAreaChangeWarehouseRecordsEntity> entities) {
        if (entities == null) {
            return new ArrayList<>();
        }
        
        List<CustomCityAreaVO> voList = new ArrayList<>();
        for (WncCityAreaChangeWarehouseRecordsEntity entity : entities) {
            CustomCityAreaVO dto = convertToCustomCityAreaVO(entity);
            if (dto != null) {
                voList.add(dto);
            }
        }
        return voList;
    }


    /**
     * 批量转换为CustomAreaDetail列表
     */
    public static List<CustomAreaDetail> convertToCustomAreaDetailList(List<WncFenceAreaChangeRecordsEntity> areaChangeRecords) {
        if (areaChangeRecords == null) {
            return new ArrayList<>();
        }
        
        List<CustomAreaDetail> result = new ArrayList<>();
        for (WncFenceAreaChangeRecordsEntity areaChangeRecord : areaChangeRecords) {
            CustomAreaDetail areaDetail = convertToCustomAreaDetail(areaChangeRecord);
            if (areaDetail != null) {
                result.add(areaDetail);
            }
        }
        return result;
    }
}
