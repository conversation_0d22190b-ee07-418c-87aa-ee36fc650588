package net.summerfarm.wnc.application.inbound.controller.fence.Input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 新增自定义区域变更记录
 * date: 2025/8/27 14:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AddCityAreaChangeWarehouseRecordCommandInput {

    /**
     * 省
     */
    @NotBlank(message = "省份不能为空")
    private String province;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 区域
     */
    @NotBlank(message = "区域不能为空")
    private String area;

}
