package net.summerfarm.wnc.application.fence;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.wnc.api.fence.input.ContactPoiDistanceCommand;
import net.summerfarm.wnc.api.fence.service.HistoryOutOfCustomFenceService;
import net.summerfarm.wnc.common.enums.AdCodeMsgEnums;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.domain.fence.*;
import net.summerfarm.wnc.domain.fence.dataObject.AddrDO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-06-19
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class HistoryOutOfCustomFenceSerivceImpl implements HistoryOutOfCustomFenceService {

    private final AdCodeMsgRepository adCodeMsgRepository;
    private final HistoryOutOfCustomFenceAreaESRepository customFenceAreaESRepository;
    private final OutLandContactRepository outLandContactRepository;
    private final FenceRepository fenceRepository;
    private final ContactGdPoiDistanceRepository contactGdPoiDistanceRepository;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initCustomFence() {
        //获取有效的城市区域
        List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgRepository.queryList(AdCodeMsgQuery.builder()
                .statusList(Arrays.asList(AdCodeMsgEnums.Status.VALID.getValue(),AdCodeMsgEnums.Status.STOP.getValue()))
                .build());
        if(CollectionUtils.isEmpty(adCodeMsgEntities)){
            return;
        }
        //adCode集合
        Set<String> adCodeSet = adCodeMsgEntities.stream()
                .map(AdCodeMsgEntity::getAdCode)
                .filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        //保存到es
        this.saveAdCode2Es(adCodeMsgEntities, adCodeSet);
    }

    /**
     * 保存到es
     * @param adCodeMsgEntities 省市区信息
     * @param adCodeSet 区域集合
     */
    private void saveAdCode2Es(List<AdCodeMsgEntity> adCodeMsgEntities, Set<String> adCodeSet) {
        //查询已经存在的
        Set<String> esadCodeSet = customFenceAreaESRepository.queryAdCodeByAdCodes(adCodeSet);
        //移除已经存在的
        adCodeSet.removeAll(esadCodeSet);
        if(CollectionUtils.isEmpty(adCodeSet)){
            log.info("数据已经全部存在");
            return;
        }

        Date date = new Date();
        for (AdCodeMsgEntity adCodeMsgEntity : adCodeMsgEntities) {
            if(StrUtil.isBlank(adCodeMsgEntity.getAdCode())){
                log.error("adCodeMsgEntity.adCode is null adCodeMsgEntity:{}",JSON.toJSONString(adCodeMsgEntity),new BizException("adCode is null"));
                continue;
            }
            if(!adCodeSet.contains(adCodeMsgEntity.getAdCode())){
                continue;
            }
            //排除柠季的特殊围栏
            if(adCodeMsgEntity.getCity().contains(",")){
                continue;
            }

            //不存在新增
            //根据ad_code请求高德获取到城市边缘POI集合
            String poiList = GaoDeUtil.queryPoiByGeoCode(adCodeMsgEntity.getAdCode());
            if(StrUtil.isBlank(poiList)){
                log.error("poiList is null adCodeMsgEntity:{}",JSON.toJSONString(adCodeMsgEntity),new BizException("未查询到对应的POI信息"));
                continue;
            }
            for (String cityPoi : poiList.split("\\|")) {
                CustomFenceAreaEntity entity = new CustomFenceAreaEntity();
                entity.setAdCode(adCodeMsgEntity.getAdCode());
                entity.setProvince(adCodeMsgEntity.getProvince());
                entity.setCity(adCodeMsgEntity.getCity());
                entity.setArea(adCodeMsgEntity.getArea());
                entity.setGeoLocation(cityPoi);
                entity.setCreateTime(date);
                //写入到ES
                customFenceAreaESRepository.save(entity);
            }
        }
    }

    @Override
    public void createCustomFenceAreaIndex() {
        customFenceAreaESRepository.createCustomFenceAreaIndex();
    }

    @Override
    public String queryPoiFence(String poi) {
        if(StrUtil.isBlank(poi)){
            throw new BizException("poi不能为空");
        }
        //查询poi对应的区域
        CustomFenceAreaEntity customFenceArea = customFenceAreaESRepository.queryPoiFence(poi);
        if(customFenceArea == null){
            return null;
        }
        //根据省市区查询对应的城配仓和围栏
        FenceEntity fence = fenceRepository.queryFenceByAddr(customFenceArea.getCity(),customFenceArea.getArea());
        if(fence != null){
            return customFenceArea.getAdCode()
                    + "-"+customFenceArea.getProvince()
                    + "-" + customFenceArea.getCity()
                    + "-" + customFenceArea.getArea()
                    + "-" + fence.getFenceName()
                    + "-" + fence.getStoreNo();
        }else{
            return customFenceArea.getAdCode()
                    + "-"+customFenceArea.getProvince()
                    + "-" + customFenceArea.getCity()
                    + "-" + customFenceArea.getArea();
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void adCodeInfo2Es(String adCode, String province, String city, String area) {
        //不存在新增
        //根据ad_code请求高德获取到城市边缘POI集合
        String poiList = GaoDeUtil.queryPoiByGeoCode(adCode);
        if(StrUtil.isBlank(poiList)){
            log.error("poiList is null adCode:{}",adCode,new Exception("未查询到对应的POI信息"));
        }
        //保存到es
        AdCodeMsgEntity adCodeMsgEntity = AdCodeMsgEntity.builder()
                .adCode(adCode)
                .province(province)
                .city(city)
                .area(area).build();
        HashSet<String> adCodeSet = new HashSet<>();
        adCodeSet.add(adCode);
        //保存到es
        this.saveAdCode2Es(Collections.singletonList(adCodeMsgEntity), adCodeSet);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void compareContactPoi(Integer beginNum, Integer endNum) {
         List<OutLandContactEntity> contactEntities = outLandContactRepository.queryListByBeginEndNum(beginNum,endNum);
         if(CollectionUtils.isEmpty(contactEntities)){
             return;
         }
        List<Integer> storeNoList = contactEntities.stream().map(OutLandContactEntity::getStoreNo).collect(Collectors.toList());
         if(CollectionUtils.isEmpty(storeNoList)){
             return;
         }
        //查询所有的围栏信息
        List<AddrDO> addrDOList = fenceRepository.queryAddrDOAll();
        Map<String, Integer> addrStoreMap = addrDOList.stream().collect(Collectors.toMap(addr -> addr.getProvince() + "#" + addr.getCity() + "#" + addr.getArea(), AddrDO::getStoreNo));
        if(CollectionUtils.isEmpty(addrStoreMap)){
            return;
        }

        List<OutLandContactEntity> saveList = new ArrayList<>();
        for (OutLandContactEntity contactEntity : contactEntities) {
            if(StrUtil.isBlank(contactEntity.getPoiNote()) || contactEntity.getStoreNo() == null){
                continue;
            }
            //排除柠季的特殊客户
            if(contactEntity.getCity() != null && contactEntity.getCity().contains(",")){
                continue;
            }

            CustomFenceAreaEntity customFence = customFenceAreaESRepository.queryPoiFence(contactEntity.getPoiNote());
            if(customFence == null){
                log.error("当前客户POI没有匹配区域,客户信息:{}", JSON.toJSONString(contactEntity),new Exception("当前客户POI没有匹配区域"));
                continue;
            }
            //围栏城配仓编号
            Integer fenceStoreNo = addrStoreMap.get(customFence.getProvince() + "#" + customFence.getCity() + "#" + customFence.getArea());
            if(Objects.equals(fenceStoreNo,contactEntity.getStoreNo()) || fenceStoreNo == null){
                continue;
            }
            OutLandContactEntity outLandContactEntity = new OutLandContactEntity();
            //客户信息
            outLandContactEntity.setContactId(contactEntity.getContactId());
            outLandContactEntity.setStoreNo(contactEntity.getStoreNo());
            outLandContactEntity.setCity(contactEntity.getCity());
            outLandContactEntity.setArea(contactEntity.getArea());
            //poi匹配信息
            outLandContactEntity.setPoiCity(customFence.getCity());
            outLandContactEntity.setPoiArea(customFence.getArea());
            outLandContactEntity.setPoiStoreNo(fenceStoreNo);

            saveList.add(outLandContactEntity);
        }
        outLandContactRepository.saveBatchCompareError(saveList);

    }

    @Override
    public Long queryContactTotal() {
        return outLandContactRepository.queryContactTotal();
    }

    @Override
    public void adCodeFenceInit(String adCode, String province, String city, String area) {
        if(StrUtil.isBlank(adCode) || StrUtil.isBlank(province) || StrUtil.isBlank(city) || StrUtil.isBlank(area)){
            throw new BizException("区域编号、省市区不能为空");
        }
        String poiList = GaoDeUtil.queryPoiByGeoCode(adCode);
        if(StrUtil.isBlank(poiList)){
            log.error("poiList is null adCode:{}",adCode,new BizException("未查询到对应的POI信息"));
            return;
        }
        //查询已经存在的
        Set<String> adCodeSet = new HashSet<>();
        adCodeSet.add(adCode);
        Set<String> esadCodeSet = customFenceAreaESRepository.queryAdCodeByAdCodes(adCodeSet);
        if(esadCodeSet.size() > 0){
            throw new BizException("该地址区域编号已存在");
        }
        for (String cityPoi : poiList.split("\\|")) {
            CustomFenceAreaEntity entity = new CustomFenceAreaEntity();
            entity.setAdCode(adCode);
            entity.setProvince(province);
            entity.setCity(city);
            entity.setArea(area);
            entity.setGeoLocation(cityPoi);
            entity.setCreateTime(new DateTime());
            //写入到ES
            customFenceAreaESRepository.save(entity);
        }
    }

    @Override
    public void contactPoiDistance(ContactPoiDistanceCommand command) {
        List<OutLandContactEntity> outLandContactEntities = new ArrayList<>();
        if(command.getBeginDay() != null && command.getEndDay() != null){
            //查询最近几天下单的客户
            outLandContactEntities = outLandContactRepository.queryContactRecentOrderDays(command.getBeginDay(),command.getEndDay());
        }else if(!CollectionUtils.isEmpty(command.getContactIds())) {
            //查询指定的客户距离信息
            outLandContactEntities = outLandContactRepository.queryByIds(command.getContactIds());
        }
        contactGdPoiDistanceRepository.contactPoiDistanceSave(outLandContactEntities);
    }

    @Override
    public void deleteContactPoiDistance() {
        contactGdPoiDistanceRepository.deleteContactPoiDistance();
    }
}

