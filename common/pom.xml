<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>summerfarm-wnc</artifactId>
        <groupId>net.summerfarm</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>summerfarm-wnc-common</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-common</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-rocketmq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-oss-support</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-authorization</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-common</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-robot-util</artifactId>
        </dependency>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- alibaba json -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <!--  lombok  -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <!-- POJO转换工具 begin -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <!-- POJO转换工具 end -->

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-redis-support</artifactId>
        </dependency>
        <dependency>
            <groupId>io.oss84.geotools</groupId>
            <artifactId>gt-geojson</artifactId>
        </dependency>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
    </dependencies>
</project>
