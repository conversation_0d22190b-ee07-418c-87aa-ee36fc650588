package net.summerfarm.wnc.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/16 15:38<br/>
 *
 * <AUTHOR> />
 */
public interface AdCodeMsgEnums {

    //状态 0正常 1失效
    @Getter
    @AllArgsConstructor
    enum Status{
        VALID(0, "正常"),
        INVALID(1, "失效"),
        STOP(3, "停用");
        ;
        private Integer value;
        private String content;

        public static List<Integer> getUsableStatus() {
            return Lists.newArrayList(AdCodeMsgEnums.Status.VALID.getValue(), AdCodeMsgEnums.Status.STOP.getValue());
        }
    }


    //区域类型 0省市区区域 1自定义区域
    @Getter
    @AllArgsConstructor
    enum AreaType{
        CITY_AREA(0, "省市区区域"),
        CUSTOM_AREA(1, "自定义区域"),
        ;
        private Integer value;
        private String content;
    }

    //区域绘制类型 0已绘制 1未绘制（其他区域）
    @Getter
    @AllArgsConstructor
    enum AreaDrawType{
        DRAWED(0, "已绘制"),
        UNDRAW(1, "未绘制"),
        ;
        private Integer value;
        private String content;
    }
}
