<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.TmsStopDeliveryMapper">

    <select id="queryRecentlyStopByStoreNoList" resultType="net.summerfarm.wnc.infrastructure.model.TmsStopDelivery">
        SELECT
            *
        FROM
            (
                SELECT * FROM `tms_stop_delivery` WHERE store_no in
                <foreach collection="storeNoList" item="storeNo" open="(" close=")" separator=",">
                    #{storeNo}
                </foreach>
                   and delete_flag=0 ORDER BY id DESC
                ) t
        GROUP BY
            t.store_no;
    </select>

</mapper>
