<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WncFenceChangeTaskMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.wnc.infrastructure.model.WncFenceChangeTask">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="fence_id" jdbcType="INTEGER" property="fenceId" />
        <result column="fence_name" jdbcType="VARCHAR" property="fenceName"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo" />
        <result column="store_no" jdbcType="INTEGER" property="storeNo" />
        <result column="change_acm_id" jdbcType="VARCHAR" property="changeAcmId"/>
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="target_no" jdbcType="INTEGER" property="targetNo" />
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="exe_time" jdbcType="TIMESTAMP" property="exeTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, fence_id, fence_name, area_no, store_no, change_acm_id, `type`, target_no, `status`, exe_time, remark, creator_id,
             creator, create_time, updater, update_time
    </sql>

    <select id="queryPage" resultMap="BaseResultMap" parameterType="net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery">
        SELECT * FROM wnc_fence_change_task
        <where>
            <if test="fenceId != null">
                AND fence_id = #{fenceId}
            </if>
            <if test="fenceName != null and fenceName != ''">
                AND fence_name LIKE CONCAT('%',#{fenceName},'%')
            </if>
            <if test="status != null and status.size > 0">
                AND `status` in
                <foreach collection="status" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="exeDateStart != null">
                AND exe_time <![CDATA[>=]]> #{exeDateStart}
            </if>
            <if test="exeDateEnd != null">
                AND exe_time <![CDATA[<=]]> #{exeDateEnd}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
