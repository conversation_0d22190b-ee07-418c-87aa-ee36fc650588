<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.CompleteDeliveryMapper">
    <select id="queryListAreaLastTime" resultType="java.lang.String">
        SELECT
        complete_delivery_time
        FROM
        `complete_delivery` t
        LEFT JOIN complete_delivery_ad_code_mapping c ON t.id = c.complete_delivery_id
        LEFT JOIN ad_code_msg a ON c.ad_code = a.ad_code
        AND a.`status` = #{query.adCodeStatus}
        WHERE
        t.`status` = #{query.completeDeliveryStatus}
        AND a.city = #{query.city}
        <if test="query.area != null and query.area != ''">
            AND a.area = #{query.area}
        </if>
    </select>
</mapper>
