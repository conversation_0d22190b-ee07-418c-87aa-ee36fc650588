<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.config.WncStockingTimeConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="wncStockingTimeConfigResultMap" type="net.summerfarm.wnc.infrastructure.model.config.WncStockingTimeConfig">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="business_no" property="businessNo" jdbcType="INTEGER"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="fulfillment_type" property="fulfillmentType" jdbcType="INTEGER"/>
		<result column="delivery_rules" property="deliveryRules" jdbcType="INTEGER"/>
		<result column="stocking_time" property="stockingTime" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wncStockingTimeConfigColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.business_no,
          t.tenant_id,
          t.fulfillment_type,
          t.delivery_rules,
          t.stocking_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="businessNo != null">
                AND t.business_no = #{businessNo}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="fulfillmentType != null">
                AND t.fulfillment_type = #{fulfillmentType}
            </if>
			<if test="deliveryRules != null">
                AND t.delivery_rules = #{deliveryRules}
            </if>
			<if test="stockingTime != null">
                AND t.stocking_time = #{stockingTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="businessNo != null">
                    t.business_no = #{businessNo},
                </if>
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="fulfillmentType != null">
                    t.fulfillment_type = #{fulfillmentType},
                </if>
                <if test="deliveryRules != null">
                    t.delivery_rules = #{deliveryRules},
                </if>
                <if test="stockingTime != null">
                    t.stocking_time = #{stockingTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wncStockingTimeConfigResultMap" >
        SELECT <include refid="wncStockingTimeConfigColumns" />
        FROM wnc_stocking_time_config t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.config.param.query.WncStockingTimeConfigQueryParam"  resultType="net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.business_no businessNo,
            t.tenant_id tenantId,
            t.fulfillment_type fulfillmentType,
            t.delivery_rules deliveryRules,
            t.stocking_time stockingTime
        FROM wnc_stocking_time_config t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.config.param.query.WncStockingTimeConfigQueryParam" resultMap="wncStockingTimeConfigResultMap" >
        SELECT <include refid="wncStockingTimeConfigColumns" />
        FROM wnc_stocking_time_config t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.config.WncStockingTimeConfig" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wnc_stocking_time_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="businessNo != null">
				  business_no,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="fulfillmentType != null">
				  fulfillment_type,
              </if>
              <if test="deliveryRules != null">
				  delivery_rules,
              </if>
              <if test="stockingTime != null">
				  stocking_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="businessNo != null">
				#{businessNo,jdbcType=INTEGER},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="fulfillmentType != null">
				#{fulfillmentType,jdbcType=INTEGER},
              </if>
              <if test="deliveryRules != null">
				#{deliveryRules,jdbcType=INTEGER},
              </if>
              <if test="stockingTime != null">
				#{stockingTime,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.config.WncStockingTimeConfig" >
        UPDATE wnc_stocking_time_config t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.config.WncStockingTimeConfig" >
        DELETE FROM wnc_stocking_time_config
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>