<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WncSkuPathMappingMapper">

    <delete id="batchDeleteByWarehouseNosSkus">
        delete from wnc_sku_path_mapping  where
        (warehouse_no,sku) in
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="item" separator="," open="(" close=")">
                (#{item.warehouseNo}, #{item.sku})
            </foreach>
        </if>
    </delete>
</mapper>
