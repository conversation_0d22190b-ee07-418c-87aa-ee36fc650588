<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.config.WncFullCategoryWarehouseSkuWhiteConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="wncFullCategoryWarehouseSkuWhiteConfigResultMap" type="net.summerfarm.wnc.infrastructure.model.config.WncFullCategoryWarehouseSkuWhiteConfig">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wncFullCategoryWarehouseSkuWhiteConfigColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.warehouse_no,
          t.sku
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
            <if test="skus != null and skus.size() > 0">
                AND t.sku in
                <foreach collection="skus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="warehouseNos != null and warehouseNos.size() > 0">
                AND t.warehouse_no in
                <foreach collection="warehouseNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wncFullCategoryWarehouseSkuWhiteConfigResultMap" >
        SELECT <include refid="wncFullCategoryWarehouseSkuWhiteConfigColumns" />
        FROM wnc_full_category_warehouse_sku_white_config t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam"  resultType="net.summerfarm.wnc.domain.config.entity.WncFullCategoryWarehouseSkuWhiteConfigEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.warehouse_no warehouseNo,
            t.sku sku
        FROM wnc_full_category_warehouse_sku_white_config t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.config.param.query.WncFullCategoryWarehouseSkuWhiteConfigQueryParam" resultMap="wncFullCategoryWarehouseSkuWhiteConfigResultMap" >
        SELECT <include refid="wncFullCategoryWarehouseSkuWhiteConfigColumns" />
        FROM wnc_full_category_warehouse_sku_white_config t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.config.WncFullCategoryWarehouseSkuWhiteConfig" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wnc_full_category_warehouse_sku_white_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="sku != null">
				  sku,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into wnc_full_category_warehouse_sku_white_config (warehouse_no, sku) values
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.warehouseNo}, #{item.sku})
        </foreach>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.config.WncFullCategoryWarehouseSkuWhiteConfig" >
        UPDATE wnc_full_category_warehouse_sku_white_config t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.config.WncFullCategoryWarehouseSkuWhiteConfig" >
        DELETE FROM wnc_full_category_warehouse_sku_white_config
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>