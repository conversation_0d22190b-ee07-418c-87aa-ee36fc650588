<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WncFenceChangeTaskDetailMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.wnc.infrastructure.model.WncFenceChangeTaskDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="outer_order_id" jdbcType="VARCHAR" property="outerOrderId"/>
        <result column="source" jdbcType="INTEGER" property="source" />
        <result column="outer_contact_id" jdbcType="VARCHAR" property="outerContactId" />
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="outer_client_id" jdbcType="VARCHAR" property="outerClientId" />
        <result column="outer_client_name" jdbcType="VARCHAR" property="outerClientName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="fulfill_confirm_time" jdbcType="TIMESTAMP" property="fulfillConfirmTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, task_id, outer_order_id, `source`, outer_contact_id, delivery_time, outer_client_id, outer_client_name,
            `status`, remark, fulfill_confirm_time, create_time, update_time
    </sql>

    <select id="queryPage" resultMap="BaseResultMap" parameterType="net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery">
        SELECT * FROM wnc_fence_change_task_detail
        <where>
            <if test="changeTaskId != null">
                AND task_id = #{changeTaskId}
            </if>
        </where>
        ORDER BY fulfill_confirm_time DESC
    </select>

    <select id="selectRetryableTaskDetails" resultMap="BaseResultMap">
        SELECT wfctd.* FROM wnc_fence_change_task_detail wfctd
        LEFT JOIN wnc_fence_change_task wfct ON wfctd.task_id = wfct.id
        WHERE DATE_FORMAT(wfct.exe_time,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) AND wfct.`status` = 2 AND wfctd.`status` = 30
    </select>

</mapper>
