<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.AreaSkuMapper">

    <select id="queryWareNoByAreaNoStoreNo" resultType="integer">
        SELECT
            s.`warehouse_no`
        FROM
            `area_sku` t
            INNER JOIN `warehouse_inventory_mapping` s on t.`sku` = s.`sku`
        where
            t.`area_no` = #{areaNo}
          and s.`store_no` in
        <foreach collection="storeNos" item="storeNo" open="(" close=")" separator=",">
            #{storeNo}
        </foreach>
        GROUP BY
            s.`warehouse_no`
    </select>

    <select id="querySkuManyWarehouse" resultType="net.summerfarm.wnc.domain.fence.entity.SkuManyWarehouseEntity">
        select DISTINCT
            wim.`store_no`,
            wim.`warehouse_no`,
            wim.`sku`,
            store.`quantity`
        from
            `warehouse_inventory_mapping` wim
                inner join area_store store on store.`area_no` = wim.`warehouse_no`
                and store.`sku` = wim.`sku`
        where
            1 = 1
          and wim.`store_no` in (
            SELECT
                `store_no`
            FROM
                `fence`
            where
                `area_no` = #{areaNo}
              and `status` = 0
        )
          and wim.sku in (
            select
                tmp.sku
            from
                (
                    select
                        area.area_name,
                        wim.sku,
                        GROUP_CONCAT(DISTINCT wsc.warehouse_name)
                    from
                        `fence` f
                            inner join `warehouse_inventory_mapping` wim on wim.`store_no` = f.`store_no`
                            inner join `warehouse_storage_center` wsc on wsc.warehouse_no = wim.warehouse_no
                            inner join `warehouse_logistics_center` wlc on wlc.store_no = f.store_no
                            inner join area area on area.area_no = f.`area_no`
                    WHERE
                        f.`status` = 0
                      and f.`area_no` = #{areaNo}
                    group by
                        area.area_name,
                        wim.sku
                    having
                        count(DISTINCT wsc.warehouse_name) > 1
                ) tmp
                    inner join area_store store on store.sku = tmp.sku
                    and store.quantity > 0
                    and store.area_no in
                    <foreach collection="warehouseNos" item="warehouseNo" open="(" close=")" separator=",">
                        #{warehouseNo}
                    </foreach>
        )
        order by
            wim.`sku` asc
    </select>
</mapper>
