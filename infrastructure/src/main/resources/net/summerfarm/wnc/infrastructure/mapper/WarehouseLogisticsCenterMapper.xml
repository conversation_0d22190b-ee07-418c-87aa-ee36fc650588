<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WarehouseLogisticsCenterMapper">

    <sql id="Base_Column_List">
        id, store_no, store_name, manage_admin_id, poi_note, address ,sot_finish_time, `status`, updater, update_time, creator, create_time,sot_finish_time,origin_store_no,close_order_type,
    close_time,update_close_time,person_contact,phone
    </sql>
    <select id="selectByStoreNo" resultType="net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsCenter">
        select
        <include refid="Base_Column_List"/>
        from warehouse_logistics_center
        where store_no = #{storeNo,jdbcType=INTEGER}
    </select>

    <select id="queryPageList" parameterType="net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsQuery" resultType="net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsCenter">
        select * from warehouse_logistics_center wlc
        <if test="stopDeliveryStatus !=null">
            left join tms_stop_delivery tsd on wlc.store_no = tsd.store_no and tsd.delete_flag = 0
        </if>
        <where>
            <if test="status != null">
                and wlc.status = #{status}
            </if>
            <if test="storeNo != null">
                and wlc.store_no = #{storeNo}
            </if>
            <if test="stopDeliveryStatus !=null and stopDeliveryStatus == 0">
                and tsd.id is null
            </if>
            <if test="stopDeliveryStatus !=null and stopDeliveryStatus == 1">
                and shutdown_end_time >= DATE(now())
            </if>
            <if test="stopDeliveryStatus !=null and stopDeliveryStatus == 2">
                and tsd.shutdown_end_time &lt; DATE(now())
            </if>
            <if test="storeNos != null and storeNos.size() > 0">
                and wlc.store_no in
                <foreach collection="storeNos" item="storeNo" open="(" close=")" separator=",">
                    #{storeNo}
                </foreach>
            </if>
            <if test="regions != null and regions.size() > 0">
                and wlc.region in
                <foreach collection="regions" item="region" open="(" close=")" separator=",">
                    #{region}
                </foreach>
            </if>
            <if test="fulfillmentType != null">
                and wlc.fulfillment_type = #{fulfillmentType}
            </if>
        </where>
        order by wlc.id asc
    </select>


    <update id="cancelUpdateCloseTime">
        update warehouse_logistics_center set update_close_time = null
        where store_no = #{storeNo}
    </update>
</mapper>
