<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.ChangeFenceMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.wnc.infrastructure.model.ChangeFence">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo" />
        <result column="fence_id" jdbcType="INTEGER" property="fenceId" />
        <result column="change_to_fence_id" jdbcType="INTEGER" property="changeToFenceId"/>
        <result column="change_to_store_no" jdbcType="INTEGER" property="changeToStoreNo" />
        <result column="change_acm_id" jdbcType="VARCHAR" property="changeAcmId"/>
        <result column="exe_time" jdbcType="TIMESTAMP" property="exeTime"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="INTEGER" property="operator"/>
        <result column="fence_name" jdbcType="VARCHAR" property="fenceName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, `type`, `status`, area_no, fence_id, change_to_fence_id, change_to_store_no, change_acm_id,
            exe_time, add_time, update_time, operator, fence_name, remark, creator, updater
    </sql>

    <select id="queryPage" resultMap="BaseResultMap" parameterType="net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery">
        SELECT * FROM change_fence
        <where>
            <if test="fenceId != null">
                AND fence_id = #{fenceId}
            </if>
            <if test="fenceName != null and fenceName != ''">
                AND fence_name LIKE CONCAT('%',#{fenceName},'%')
            </if>
            <if test="status != null and status.size > 0">
                AND `status` in
                <foreach collection="status" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="exeDateStart != null">
                AND exe_time <![CDATA[>=]]> #{exeDateStart}
            </if>
            <if test="exeDateEnd != null">
                AND exe_time <![CDATA[<=]]> #{exeDateEnd}
            </if>
            AND creator IS NOT NULL
        </where>
          ORDER BY add_time DESC
    </select>
</mapper>
