<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WarehouseInventoryMappingMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.wnc.infrastructure.model.WarehouseInventoryMapping">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
        <result column="store_no" jdbcType="INTEGER" property="storeNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="sale_lock_quantity" jdbcType="INTEGER" property="saleLockQuantity"/>
        <result column="updater" jdbcType="INTEGER" property="updater" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator" jdbcType="INTEGER" property="creator" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, warehouse_no, store_no, sku, sale_lock_quantity, updater, update_time, creator, create_time
    </sql>

    <select id="selectListBySkuStoreNo" resultMap="BaseResultMap" parameterType="net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery">
        SELECT wim.* FROM warehouse_inventory_mapping wim
        LEFT JOIN warehouse_logistics_center wlc ON wim.store_no = wlc.store_no
        LEFT JOIN warehouse_storage_center wsc ON wim.warehouse_no = wsc.warehouse_no AND wsc.tenant_id = 1
        WHERE wlc.status = 1 AND wsc.status = 1
        AND wlc.store_no IN
        <if test="list != null and list.size > 0">
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.storeNo}
        </foreach>
        </if>
        AND (wim.store_no, wim.sku) IN
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="item" separator="," open="(" close=")">
                (#{item.storeNo}, #{item.sku})
            </foreach>
        </if>
    </select>

</mapper>
