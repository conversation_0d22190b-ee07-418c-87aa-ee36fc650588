<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.FenceMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.wnc.infrastructure.model.Fence">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="fence_name" jdbcType="VARCHAR" property="fenceName"/>
        <result column="store_no" jdbcType="INTEGER" property="storeNo" />
        <result column="area_no" jdbcType="INTEGER" property="areaNo" />
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="admin_id" jdbcType="INTEGER" property="adminId" />
        <result column="pack_id" jdbcType="INTEGER" property="packId"/>
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="areaName" jdbcType="VARCHAR" property="areaName"/>
        <result column="storeName" jdbcType="VARCHAR" property="storeName"/>
        <result column="adminName" jdbcType="VARCHAR" property="adminName"/>
        <result column="cityName" jdbcType="VARCHAR" property="cityName"/>
        <result column="order_channel_type" jdbcType="VARCHAR" property="orderChannelType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fence_name,store_no,area_no,`status`,add_time,update_time,admin_id,pack_id,`type`
    </sql>

    <select id="queryPage" resultMap="BaseResultMap" parameterType="net.summerfarm.wnc.common.query.fence.FencePageQuery">
        SELECT
        f.id,
        f.fence_name,
        f.store_no,
        f.area_no,
        f.status,
        f.add_time,
        f.update_time,
        f.admin_id,
        f.pack_id,
        f.type,
        a.area_name AS areaName,
        wlc.store_name AS storeName,
        ad.realname AS adminName,
        adc.city AS cityName
        FROM fence f
        LEFT JOIN area a ON f.area_no = a.area_no
        LEFT JOIN warehouse_logistics_center wlc ON f.store_no = wlc.store_no
        LEFT JOIN admin ad ON f.admin_id = ad.admin_id
        LEFT JOIN (
        SELECT city,area,fence_id FROM ad_code_msg
        WHERE status IN (0,3)
        GROUP BY fence_id
        )  adc ON adc.fence_id = f.id
        <where>
            <if test="fenceName != null">
                AND f.fence_name LIKE CONCAT('%', #{fenceName},'%')
            </if>
            <if test="storeNo != null">
                AND f.store_no = #{storeNo}
            </if>
            <if test="areaNo != null">
                AND f.area_no = #{areaNo}
            </if>
            <if test="cityName != null">
                AND adc.city = #{cityName}
            </if>
            <if test="type != null">
                AND f.type = #{type}
            </if>
            <if test="status != null">
                AND f.status = #{status}
            </if>
            <if test="statusList != null">
                AND f.status IN
                <foreach collection="statusList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY f.id DESC
    </select>

    <select id="selectMaxPackId" resultType="java.lang.Integer" >
        select ifnull(max(pack_id),0) from fence
    </select>

    <select id="queryDetail" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
            f.id,
            f.fence_name,
            f.store_no,
            f.area_no,
            f.status,
            f.add_time,
            f.update_time,
            f.admin_id,
            f.pack_id,
            f.type,
            a.area_name AS areaName,
            wlc.store_name AS storeName,
            ad.realname AS adminName,
            f.order_channel_type
        from fence f
        left join area a on f.area_no = a.area_no
        left join admin ad on ad.admin_id = f.admin_id
        left join warehouse_logistics_center wlc on wlc.store_no = f.store_no
        where f.id = #{fenceId}
    </select>
    <select id="queryAddrDOByStoreNoList" resultType="net.summerfarm.wnc.domain.fence.dataObject.AddrDO">
        SELECT
           f.fence_name as fenceName,
           f.store_no as storeNo,
           f.status as fenceStatus,
           a.ad_code as adCode,
           a.province,
           a.city,
           a.area,
           a.status as adCodeStatus,
           a.fence_id as fenceId
        FROM
        fence f
        LEFT JOIN ad_code_msg a ON f.id = a.fence_id
        WHERE
        f.`status` = 0
        AND a.`status` =0
        <if test="storeNoList!= null and storeNoList.size() > 0">
            AND f.store_no IN
            <foreach collection="storeNoList" item="storeNo" open="(" separator="," close=")">
                {storeNo}
            </foreach>
        </if>
    </select>

    <select id="queryAreaSkuManyWarehouse" resultType="net.summerfarm.wnc.domain.fence.dataObject.AreaSkuManyWarehouseDO">
        select
            area.area_name as areaName,
            wim.sku,
            GROUP_CONCAT(DISTINCT wsc.warehouse_name) as manyWarehouseName,
            area.`area_no` as areaNo,
            GROUP_CONCAT(DISTINCT f.`fence_name`) as manyFenceName,
            GROUP_CONCAT(DISTINCT wlc.store_name) as manyStoreName
        from
            `fence` f
                inner join `warehouse_inventory_mapping` wim on wim.`store_no` = f.`store_no`
                inner join `warehouse_storage_center` wsc on wsc.warehouse_no = wim.warehouse_no and wsc.`status` = 1
                inner join `warehouse_logistics_center` wlc on wlc.store_no = f.store_no and wlc.`status` = 1
                inner join area area on area.area_no = f.`area_no`
        WHERE
            f.`status` = 0 and f.`area_no` = #{areaNo}
        group by
            area.area_name,
            wim.sku
        having
            count(DISTINCT wsc.warehouse_name) > 1
    </select>

</mapper>
