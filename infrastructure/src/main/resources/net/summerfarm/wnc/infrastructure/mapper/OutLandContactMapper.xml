<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.OutLandContactMapper">

    <select id="queryNingjiList" resultType="net.summerfarm.wnc.infrastructure.model.OutLandContact">
        SELECT c.* FROM `contact` c LEFT JOIN `merchant` m on c.`m_id`  = m.`m_id` where m.`admin_id` in (11365,12016,11803) and (c.`poi_note` is NULL or c.`poi_note`  = '')
    </select>

    <select id="queryContactOrdersDays" resultType="net.summerfarm.wnc.infrastructure.model.OutLandContact">
        SELECT DISTINCT
            t.*
        FROM
        contact t
        LEFT JOIN delivery_plan s ON t.contact_id = s.contact_id
        WHERE
        s.delivery_time &gt;= #{beginDay}
        AND s.delivery_time &lt;= #{endDay}
    </select>
</mapper>
