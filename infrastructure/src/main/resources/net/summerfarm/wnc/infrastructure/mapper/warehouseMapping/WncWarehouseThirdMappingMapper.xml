<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.warehouseMapping.WncWarehouseThirdMappingMapper">
    <!-- 结果集映射 -->
    <resultMap id="wncWarehouseThirdMappingResultMap" type="net.summerfarm.wnc.infrastructure.model.warehouseMapping.WncWarehouseThirdMapping">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="third_source_name" property="thirdSourceName" jdbcType="VARCHAR"/>
		<result column="third_warehouse_no" property="thirdWarehouseNo" jdbcType="VARCHAR"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="open_platform_app_key" property="openPlatformAppKey" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wncWarehouseThirdMappingColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.warehouse_no,
          t.third_source_name,
          t.third_warehouse_no,
          t.tenant_id,
          t.open_platform_app_key
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="thirdSourceName != null and thirdSourceName !=''">
                AND t.third_source_name = #{thirdSourceName}
            </if>
			<if test="thirdWarehouseNo != null and thirdWarehouseNo !=''">
                AND t.third_warehouse_no = #{thirdWarehouseNo}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="openPlatformAppKey != null and openPlatformAppKey !=''">
                AND t.open_platform_app_key = #{openPlatformAppKey}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="thirdSourceName != null">
                    t.third_source_name = #{thirdSourceName},
                </if>
                <if test="thirdWarehouseNo != null">
                    t.third_warehouse_no = #{thirdWarehouseNo},
                </if>
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="openPlatformAppKey != null">
                    t.open_platform_app_key = #{openPlatformAppKey},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wncWarehouseThirdMappingResultMap" >
        SELECT <include refid="wncWarehouseThirdMappingColumns" />
        FROM wnc_warehouse_third_mapping t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam"  resultType="net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.warehouse_no warehouseNo,
            t.third_source_name thirdSourceName,
            t.third_warehouse_no thirdWarehouseNo,
            t.tenant_id tenantId,
            t.open_platform_app_key openPlatformAppKey
        FROM wnc_warehouse_third_mapping t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam" resultMap="wncWarehouseThirdMappingResultMap" >
        SELECT <include refid="wncWarehouseThirdMappingColumns" />
        FROM wnc_warehouse_third_mapping t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.warehouseMapping.WncWarehouseThirdMapping" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wnc_warehouse_third_mapping
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="thirdSourceName != null">
				  third_source_name,
              </if>
              <if test="thirdWarehouseNo != null">
				  third_warehouse_no,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="openPlatformAppKey != null">
				  open_platform_app_key,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="thirdSourceName != null">
				#{thirdSourceName,jdbcType=VARCHAR},
              </if>
              <if test="thirdWarehouseNo != null">
				#{thirdWarehouseNo,jdbcType=VARCHAR},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="openPlatformAppKey != null">
				#{openPlatformAppKey,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.warehouseMapping.WncWarehouseThirdMapping" >
        UPDATE wnc_warehouse_third_mapping t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.warehouseMapping.WncWarehouseThirdMapping" >
        DELETE FROM wnc_warehouse_third_mapping
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>