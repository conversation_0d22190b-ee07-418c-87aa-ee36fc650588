<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WncChangeFenceDetailMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.wnc.infrastructure.model.WncChangeFenceDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="change_fence_id" jdbcType="INTEGER" property="changeFenceId" />
        <result column="outer_order_id" jdbcType="VARCHAR" property="outerOrderId"/>
        <result column="source" jdbcType="INTEGER" property="source" />
        <result column="outer_contact_id" jdbcType="VARCHAR" property="outerContactId" />
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="outer_client_id" jdbcType="VARCHAR" property="outerClientId" />
        <result column="outer_client_name" jdbcType="VARCHAR" property="outerClientName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="fulfill_confirm_time" jdbcType="TIMESTAMP" property="fulfillConfirmTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, change_fence_id, outer_order_id, `source`, outer_contact_id, delivery_time, outer_client_id, outer_client_name,
            `status`, remark, fulfill_confirm_time, create_time, update_time
    </sql>

    <select id="queryPage" resultMap="BaseResultMap" parameterType="net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery">
        SELECT * FROM wnc_change_fence_detail
        <where>
            <if test="changeFenceId != null">
                AND change_fence_id = #{changeFenceId}
            </if>
        </where>
        ORDER BY fulfill_confirm_time DESC
    </select>

    <select id="selectRetryableTaskDetails" resultMap="BaseResultMap">
        SELECT wcfd.* FROM wnc_change_fence_detail wcfd
        LEFT JOIN change_fence cf ON wcfd.change_fence_id = cf.id
        WHERE DATE_FORMAT(cf.exe_time,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) AND cf.`status` = 2 AND wcfd.`status` = 30
    </select>
</mapper>
