<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WncContactConfigMapper">

    <select id="queryPopNoHaveStoreNoMonit" resultType="net.summerfarm.wnc.domain.config.monit.PopMerchantMonit">
        SELECT c.contact_id as contactId, m.`m_id` as merchantId, m.`mname` as mname
        FROM contact c
                 LEFT JOIN merchant m ON c.m_id = m.m_id
        WHERE NOT EXISTS (SELECT 1
                          FROM wnc_contact_config w
                          WHERE w.outer_contact_id = c.contact_id)
          AND m.admin_id = #{adminId} and c.status = 1
        limit 50
    </select>
</mapper>
