<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WarehouseLogisticsMappingMapper">


    <select id="queryValidWarehouseMapping" resultType="net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsMapping">
        select wlm.*
        from warehouse_logistics_mapping wlm  left join
        warehouse_storage_center wsc on wsc.warehouse_no = wlm.warehouse_no
        where wsc.status = 1
        AND wlm.store_no IN
        <foreach collection="storeNoList" item="storeNo" open="(" close=")" separator=",">
            #{storeNo}
        </foreach>
        ORDER BY wlm.create_time asc
    </select>
</mapper>
