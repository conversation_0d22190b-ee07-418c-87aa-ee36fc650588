<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WncTenantStoreSkuBlackConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="wncTenantStoreSkuBlackConfigResultMap" type="net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
        <result column="store_no" property="storeNo" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wncTenantStoreSkuBlackConfigColumns">
        t.id,
          t.create_time,
          t.update_time,
          t.tenant_id,
          t.store_no,
          t.sku
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
            <if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
            <if test="storeNo != null">
                AND t.store_no = #{storeNo}
            </if>
            <if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
        </trim>
    </sql>

    <!-- 修改字段SQL -->
    <sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">
                t.create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                t.update_time = #{updateTime},
            </if>
            <if test="tenantId != null">
                t.tenant_id = #{tenantId},
            </if>
            <if test="storeNo != null">
                t.store_no = #{storeNo},
            </if>
            <if test="sku != null">
                t.sku = #{sku},
            </if>
        </trim>
    </sql>

    <!-- 根据主键ID获取数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="wncTenantStoreSkuBlackConfigResultMap" >
        SELECT <include refid="wncTenantStoreSkuBlackConfigColumns" />
        FROM wnc_tenant_store_sku_black_config t
        WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.config.param.query.WncTenantStoreSkuBlackConfigQueryParam"  resultType="net.summerfarm.wnc.domain.config.entity.TenantStoreSkuBlackConfigEntity" >
        SELECT
        t.id id,
        t.create_time createTime,
        t.update_time updateTime,
        t.tenant_id tenantId,
        t.store_no storeNo,
        t.sku sku
        FROM wnc_tenant_store_sku_black_config t
        <include refid="whereColumnBySelect" />
        ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.config.param.query.WncTenantStoreSkuBlackConfigQueryParam" resultMap="wncTenantStoreSkuBlackConfigResultMap" >
        SELECT <include refid="wncTenantStoreSkuBlackConfigColumns" />
        FROM wnc_tenant_store_sku_black_config t
        <include refid="whereColumnBySelect"></include>
    </select>



    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
    <insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wnc_tenant_store_sku_black_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id,jdbcType=NUMERIC},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=NUMERIC},
            </if>
            <if test="storeNo != null">
                #{storeNo,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 根据主键ID进行修改 -->
    <update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig" >
        UPDATE wnc_tenant_store_sku_black_config t
        <include refid="whereColumnByUpdate"></include>
        <where>
            t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



    <!-- 根据主键ID进行物理删除 -->
    <delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.WncTenantStoreSkuBlackConfig" >
        DELETE FROM wnc_tenant_store_sku_black_config
        WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

</mapper>
