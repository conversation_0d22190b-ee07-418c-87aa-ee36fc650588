<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.AdCodeMsgMapper">

    <select id="queryValidList" resultType="net.summerfarm.wnc.infrastructure.model.AdCodeMsg">
        select province,city
        from ad_code_msg
        where `status` IN (0,3) AND city in
        <foreach collection="cityList" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
        group by province,city


    </select>

    <select id="selectByCityAndNotFenceId"  resultType="net.summerfarm.wnc.infrastructure.model.AdCodeMsg">
        select
        id,
        ad_code,
        province,
        city,
        area,
        `level`,
        gd_id,
        fence_id
        from ad_code_msg
        where
        status in (0,3) and  city = #{city}
        <if test="fenceId != null">
            and  fence_id != #{fenceId}
        </if>
    </select>

    <select id="queryAdCodeMsgByStoreNoAddrStatus" parameterType="net.summerfarm.wnc.common.query.fence.AdCodeMsgByStoreNoAddrStatusQuery" resultType="net.summerfarm.wnc.infrastructure.model.AdCodeMsg">
        SELECT
            t.*
        FROM
            ad_code_msg t
            LEFT JOIN fence f ON t.fence_id=f.id
            LEFT JOIN warehouse_logistics_center wlc ON wlc.store_no=f.store_no
        <where>
            <if test="storeNo != null">
                and f.store_no = #{storeNo}
            </if>
            <if test="adCodeMsgStatus != null">
                and t.status = #{adCodeMsgStatus}
            </if>
            <if test="adCode != null and adCode != ''">
                and t.ad_code = #{adCode}
            </if>
            <if test="fenceStatus != null">
                and f.status = #{fenceStatus}
            </if>
            <if test="warehouseLogisticsCenterStatus != null">
                and wlc.status = #{warehouseLogisticsCenterStatus}
            </if>
            <if test="provinceCityAreaLikeName != null and provinceCityAreaLikeName != ''">
                and concat(t.province,t.city,t.area) like concat('%',#{provinceCityAreaLikeName},'%')
            </if>
            <if test="cityLikeName != null and cityLikeName != ''">
                and t.city like concat('%',#{cityLikeName},'%')
            </if>
        </where>
    </select>

    <select id="queryAdCodeMsgFenceStore" parameterType="net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery" resultType="net.summerfarm.wnc.domain.fence.dataObject.AdCodeMsgFenceStoreDO">
        select acm.*,
                f.fence_name,
                f.id       as fenceId,
                f.fence_name,
                f.status   as fenceStatus,
                wlc.status as storeStatus,
                wlc.store_name,
                wlc.store_no
        from ad_code_msg acm
                 left join fence f on acm.fence_id = f.id
                 left join warehouse_logistics_center wlc on f.store_no = wlc.store_no
        <where>
            <if test="fenceId != null">
                and acm.fence_id = #{fenceId}
            </if>
            <if test="province != null and province != ''">
                and acm.province = #{province}
            </if>
            <if test="city != null and city != ''">
                and acm.city = #{city}
            </if>
            <if test="area != null and area != ''">
                and acm.area = #{area}
            </if>
            <if test="status != null">
                and acm.status = #{status}
            </if>
            <if test="adCode != null and adCode != ''">
                and acm.ad_code = #{adCode}
            </if>
            <if test="adCodeList != null and adCodeList.size > 0">
                AND acm.ad_code in
                <foreach collection="adCodeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="fenceStatus != null">
                and f.status = #{fenceStatus}
            </if>
            <if test="storeStatus != null">
                and wlc.status = #{storeStatus}
            </if>
            <if test="storeNos != null and storeNos.size > 0">
                AND wlc.store_no in
                <foreach collection="storeNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="unbindAndInvalidAreaByFenceIds">
        UPDATE ad_code_msg
        SET fence_id = null,
            status = #{status}
        WHERE fence_id in
        <foreach collection="fenceIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
