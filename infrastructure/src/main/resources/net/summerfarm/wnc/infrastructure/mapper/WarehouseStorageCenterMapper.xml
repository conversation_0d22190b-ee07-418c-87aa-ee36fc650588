<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.WarehouseStorageCenterMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="person_contact" jdbcType="VARCHAR" property="personContact"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <collection property="warehouseStorageFenceEntities" ofType="net.summerfarm.wnc.domain.warehouse.entity.WncWarehouseStorageFenceEntity">
            <result column="province" property="province"/>
            <result column="city" property="city"/>
            <result column="area" property="area"/>
            <result column="fence_create_time" property="createTime"/>
            <result column="fence_update_time" property="updateTime"/>
            <result column="last_operator_name" jdbcType="VARCHAR" property="lastOperatorName"/>
        </collection>
    </resultMap>


    <select id="querySelfWarehouseStorage" parameterType="net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery" resultMap="BaseResultMap">
        SELECT
            t.id,t.warehouse_no,t.warehouse_name,t.status,t.person_contact,t.phone,t.create_time,t.update_time,
               s.province,s.city,s.area,s.create_time fence_create_time,s.update_time fence_update_time,s.last_operator_name
        FROM
            warehouse_storage_center t
                LEFT JOIN wnc_warehouse_storage_fence s ON t.warehouse_no = s.warehouse_no
                AND t.tenant_id = s.tenant_id
        <where>
            <if test="warehouseNos != null and warehouseNos.size > 0">
                AND t.warehouse_no IN
                <foreach collection="warehouseNos" item="warehouseNo" open="(" close=")" separator=",">
                    #{warehouseNo}
                </foreach>
            </if>
            <if test="provinces != null and provinces.size > 0">
                AND s.province IN
                <foreach collection="provinces" item="province" open="(" close=")" separator=",">
                    #{province}
                </foreach>
            </if>
            <if test="citys != null and citys.size > 0">
                AND s.city IN
                <foreach collection="citys" item="city" open="(" close=")" separator=",">
                    #{city}
                </foreach>
            </if>
            <if test="areas != null and areas.size > 0">
                AND s.area IN
                <foreach collection="areas" item="area" open="(" close=")" separator=",">
                    #{area}
                </foreach>
            </if>
            <if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
        </where>
        <if test="desc != null and  desc == 1">
            order by t.create_time desc
        </if>
        <if test="desc != null and  desc == 2">
            order by s.update_time desc
        </if>
    </select>

    <select id="queryProxyWarehouseStoragePage" parameterType="net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery" resultMap="BaseResultMap">
        SELECT
        DISTINCT
        t.id,
        t.warehouse_no,
        t.warehouse_name,
        t.STATUS,
        t.person_contact,
        t.phone,
        t.create_time,
        t.update_time,
        a.province,
        a.city,
        a.area
        FROM
        warehouse_storage_center t
        LEFT JOIN warehouse_logistics_mapping m on t.warehouse_no=m.warehouse_no
        LEFT JOIN fence f on m.store_no=f.store_no and f.`status`=0
        LEFT JOIN ad_code_msg a on a.fence_id=f.id and a.`status`=0
        <where>
            <if test="warehouseNos != null and warehouseNos.size > 0">
              t.warehouse_no in
                <foreach collection="warehouseNos" item="warehouseNo" open="(" close=")" separator=",">
                    #{warehouseNo}
                </foreach>
          </if>
            <if test="provinces != null and provinces.size > 0">
                AND a.province IN
                <foreach collection="provinces" item="province" open="(" close=")" separator=",">
                    #{province}
                </foreach>
            </if>
            <if test="citys != null and citys.size > 0">
                AND a.city IN
                <foreach collection="citys" item="city" open="(" close=")" separator=",">
                    #{city}
                </foreach>
            </if>
            <if test="areas != null and areas.size > 0">
                AND a.area IN
                <foreach collection="areas" item="area" open="(" close=")" separator=",">
                    #{area}
                </foreach>
            </if>
        </where>
        ORDER BY CONVERT(warehouse_name USING gbk) ASC
    </select>
</mapper>
