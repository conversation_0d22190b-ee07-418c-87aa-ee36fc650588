package net.summerfarm.wnc.infrastructure.es.converter;

import cn.hutool.core.util.StrUtil;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEntity;
import net.summerfarm.wnc.infrastructure.es.document.HistoryOutOfCustomFenceAreaDoc;

/**
 * Description: 转换类<br/>
 * date: 2023/12/6 15:26<br/>
 *
 * <AUTHOR> />
 */
public class CustomFenceAreaDocConverter {

    public static CustomFenceAreaEntity model2Entity(HistoryOutOfCustomFenceAreaDoc customFenceAreaDoc) {
        if (customFenceAreaDoc == null) {
            return null;
        }
        CustomFenceAreaEntity customFenceAreaEntity = new CustomFenceAreaEntity();
        customFenceAreaEntity.setAdCode(customFenceAreaDoc.getAdCode());
        customFenceAreaEntity.setProvince(customFenceAreaDoc.getProvince());
        customFenceAreaEntity.setCity(customFenceAreaDoc.getCity());
        customFenceAreaEntity.setArea(customFenceAreaDoc.getArea());
        customFenceAreaEntity.setCreateTime(customFenceAreaDoc.getCreateTime());
        customFenceAreaEntity.setUpdateTime(customFenceAreaDoc.getUpdateTime());
        customFenceAreaEntity.setGeoLocation(customFenceAreaDoc.getGeoLocation());

        return customFenceAreaEntity;
    }


    public static HistoryOutOfCustomFenceAreaDoc entity2Model(CustomFenceAreaEntity customFenceAreaEntity) {
        if (customFenceAreaEntity == null) {
            return null;
        }
        HistoryOutOfCustomFenceAreaDoc customFenceAreaDoc = new HistoryOutOfCustomFenceAreaDoc();
        customFenceAreaDoc.setAdCode(customFenceAreaEntity.getAdCode());
        customFenceAreaDoc.setProvince(customFenceAreaEntity.getProvince());
        customFenceAreaDoc.setCity(customFenceAreaEntity.getCity());
        customFenceAreaDoc.setArea(customFenceAreaEntity.getArea());
        customFenceAreaDoc.setCreateTime(customFenceAreaEntity.getCreateTime());
        customFenceAreaDoc.setUpdateTime(customFenceAreaEntity.getUpdateTime());
        //批量处理 点位信息

        if(StrUtil.isNotBlank(customFenceAreaEntity.getGeoLocation())){
            customFenceAreaDoc.setGeoLocation("POLYGON(("+customFenceAreaEntity.getGeoLocation()
                    .replace(",", " ")
                    .replace(";", ",")
                    +"))");
        }

        return customFenceAreaDoc;
    }
}

