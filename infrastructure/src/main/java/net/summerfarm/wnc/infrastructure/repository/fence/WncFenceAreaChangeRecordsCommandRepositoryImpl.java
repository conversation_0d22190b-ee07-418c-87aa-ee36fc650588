package net.summerfarm.wnc.infrastructure.repository.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceAreaChangeRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncFenceAreaChangeRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncFenceAreaChangeRecordsCommandRepositoryImpl implements WncFenceAreaChangeRecordsCommandRepository {

    @Autowired
    private WncFenceAreaChangeRecordsMapper wncFenceAreaChangeRecordsMapper;
    @Override
    public WncFenceAreaChangeRecordsEntity insertSelective(WncFenceAreaChangeRecordsCommandParam param) {
        WncFenceAreaChangeRecords wncFenceAreaChangeRecords = WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecords(param);
        wncFenceAreaChangeRecordsMapper.insertSelective(wncFenceAreaChangeRecords);
        return WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecordsEntity(wncFenceAreaChangeRecords);
    }

    @Override
    public int updateSelectiveById(WncFenceAreaChangeRecordsCommandParam param){
        return wncFenceAreaChangeRecordsMapper.updateSelectiveById(WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecords(param));
    }


    @Override
    public int remove(Long id) {
        return wncFenceAreaChangeRecordsMapper.remove(id);
    }

    @Override
    public void updateRecordsFenceId(List<Long> areaIds, Integer fenceId) {
        if (CollectionUtils.isEmpty(areaIds) || fenceId == null){
            return;
        }
        wncFenceAreaChangeRecordsMapper.updateRecordsFenceId(areaIds, fenceId);
    }

    @Override
    public void batchUpdate(List<WncFenceAreaChangeRecordsCommandParam> params) {
        if (CollectionUtils.isEmpty(params)){
            return;
        }
        List<WncFenceAreaChangeRecords> records = params.stream().map(WncFenceAreaChangeRecordsConverter::toWncFenceAreaChangeRecords).collect(Collectors.toList());
        wncFenceAreaChangeRecordsMapper.batchUpdate(records);
    }
}