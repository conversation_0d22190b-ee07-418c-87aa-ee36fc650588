package net.summerfarm.wnc.infrastructure.es.document;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.IndexId;
import cn.easyes.annotation.IndexName;
import cn.easyes.annotation.rely.FieldType;
import lombok.Data;

import java.util.Date;

/**
 * Description: 自定义围栏区域<br/>
 * date: 2023/12/6 14:14<br/>
 *
 * <AUTHOR> />
 */
@Deprecated
@Data
@IndexName("custom_fence_area_index")
public class HistoryOutOfCustomFenceAreaDoc {

    @IndexId
    private String id;
    /**
     * 区域编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String adCode;

    /**
     * 省
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String province;

    /**
     * 城市
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String city;

    /**
     * 区域
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String area;

    /**
     * POI信息
     */
    @IndexField(fieldType = FieldType.GEO_SHAPE)
    private String geoLocation;

    /**
     * 创建时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private Date createTime;

    /**
     * 更新时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private Date updateTime;

}
