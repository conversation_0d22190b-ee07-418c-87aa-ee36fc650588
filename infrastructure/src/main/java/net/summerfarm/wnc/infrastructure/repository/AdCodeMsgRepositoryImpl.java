package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgByStoreNoAddrStatusQuery;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.dataObject.AdCodeMsgFenceStoreDO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.infrastructure.converter.AdCodeMsgConvert;
import net.summerfarm.wnc.infrastructure.mapper.AdCodeMsgMapper;
import net.summerfarm.wnc.infrastructure.model.AdCodeMsg;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-14
 **/
@Service
public class AdCodeMsgRepositoryImpl implements AdCodeMsgRepository {
	@Resource
	private AdCodeMsgMapper adCodeMsgMapper;
	@Resource
	private AdCodeMsgConvert adCodeMsgConvert;

	@Override
	public List<AdCodeMsgEntity> queryByCityList(List<String> cityList) {
		if (CollectionUtils.isEmpty(cityList)) {
			return null;
		}
		List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.queryValidList(cityList);
		return adCodeMsgConvert.doToEntityList(adCodeMsgs);
	}

	@Override
	public List<AdCodeMsgEntity> queryList(AdCodeMsgQuery adCodeMsgQuery) {
		List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectList(new LambdaQueryWrapper<AdCodeMsg>()
				.eq(adCodeMsgQuery.getFenceId() != null, AdCodeMsg::getFenceId, adCodeMsgQuery.getFenceId())
				.eq(adCodeMsgQuery.getCity() != null, AdCodeMsg::getCity, adCodeMsgQuery.getCity())
				.in(!CollectionUtils.isEmpty(adCodeMsgQuery.getAreaIds()), AdCodeMsg::getId, adCodeMsgQuery.getAreaIds())
				.in(!CollectionUtils.isEmpty(adCodeMsgQuery.getFenceIds()), AdCodeMsg::getFenceId, adCodeMsgQuery.getFenceIds())
				.in(!CollectionUtils.isEmpty(adCodeMsgQuery.getStatusList()), AdCodeMsg::getStatus, adCodeMsgQuery.getStatusList())
				.eq(adCodeMsgQuery.getStatus() != null,AdCodeMsg::getStatus,adCodeMsgQuery.getStatus())
		);
		return adCodeMsgConvert.doToEntityList(adCodeMsgs);
	}

	@Override
	public int update(AdCodeMsgEntity adCodeMsgEntity) {
		if (adCodeMsgEntity == null){
			return 0;
		}
		AdCodeMsg adCodeMsg = adCodeMsgConvert.entity2do(adCodeMsgEntity);
		return adCodeMsgMapper.updateById(adCodeMsg);
	}

	@Override
	public List<AdCodeMsgEntity> queryByCityAndNotFenceId(String city, Integer fenceId) {
		if (StrUtil.isBlank(city)){
			return Collections.emptyList();
		}
		List<AdCodeMsg> adCodeMsgList = adCodeMsgMapper.selectByCityAndNotFenceId(city, fenceId);
		return adCodeMsgConvert.doToEntityList(adCodeMsgList);
	}

	@Override
	public PageInfo<AdCodeMsgFenceStoreDO> queryPage(AdCodeMsgQuery adCodeMsgQuery) {
		PageHelper.startPage(adCodeMsgQuery.getPageIndex(), adCodeMsgQuery.getPageSize());
		List<AdCodeMsgFenceStoreDO> adCodeMsgFenceStoreDOS = adCodeMsgMapper.queryAdCodeMsgFenceStore(adCodeMsgQuery);
		return PageInfoHelper.createPageInfo(adCodeMsgFenceStoreDOS);
	}

	@Override
	public List<AdCodeMsgEntity> queryAdCodeMsgByStoreNoAddrStatus(AdCodeMsgByStoreNoAddrStatusQuery query) {
		List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.queryAdCodeMsgByStoreNoAddrStatus(query);
		return adCodeMsgConvert.doToEntityList(adCodeMsgs);
	}

	@Override
	public void unbindAndInvalidAreaByFenceIds(List<Integer> fenceIds, Integer status) {
		if (CollectionUtils.isEmpty(fenceIds) || status == null) {
			return;
		}

		adCodeMsgMapper.unbindAndInvalidAreaByFenceIds(fenceIds, status);
	}

	@Override
	public void save(List<AdCodeMsgEntity> adCodeMsgDetailList) {
		if (CollectionUtils.isEmpty(adCodeMsgDetailList)) {
			return;
		}
		for (AdCodeMsgEntity adCodeMsgEntity : adCodeMsgDetailList) {
			AdCodeMsg adCodeMsg = adCodeMsgConvert.entity2do(adCodeMsgEntity);
			adCodeMsgMapper.insert(adCodeMsg);

			adCodeMsgEntity.setId(adCodeMsg.getId());
		}
	}
}
