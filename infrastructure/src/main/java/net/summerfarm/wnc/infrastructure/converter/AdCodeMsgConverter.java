package net.summerfarm.wnc.infrastructure.converter;

import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.infrastructure.model.AdCodeMsg;

/**
 * Description: <br/>
 * date: 2023/3/16 15:42<br/>
 *
 * <AUTHOR> />
 */
public class AdCodeMsgConverter {

    public static AdCodeMsgEntity adCodeMsg2Entity(AdCodeMsg adCodeMsg){
        if(adCodeMsg == null){
            return null;
        }
        AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();

        adCodeMsgEntity.setId(adCodeMsg.getId());
        adCodeMsgEntity.setAdCode(adCodeMsg.getAdCode());
        adCodeMsgEntity.setProvince(adCodeMsg.getProvince());
        adCodeMsgEntity.setCity(adCodeMsg.getCity());
        adCodeMsgEntity.setArea(adCodeMsg.getArea());
        adCodeMsgEntity.setLevel(adCodeMsg.getLevel());
        adCodeMsgEntity.setGdId(adCodeMsg.getGdId());
        adCodeMsgEntity.setAddTime(adCodeMsg.getAddTime());
        adCodeMsgEntity.setUpdateTime(adCodeMsg.getUpdateTime());
        adCodeMsgEntity.setStatus(adCodeMsg.getStatus());
        adCodeMsgEntity.setFenceId(adCodeMsg.getFenceId());
        adCodeMsgEntity.setCustomAreaName(adCodeMsg.getCustomAreaName());
        adCodeMsgEntity.setAreaType(adCodeMsg.getAreaType());
        adCodeMsgEntity.setAreaDrawType(adCodeMsg.getAreaDrawType());

        return adCodeMsgEntity;
    }

    public static AdCodeMsg entity2po(AdCodeMsgEntity adCodeMsgEntity){
        if (adCodeMsgEntity == null){
            return null;
        }
        AdCodeMsg adCodeMsg = new AdCodeMsg();
        adCodeMsg.setId(adCodeMsgEntity.getId());
        adCodeMsg.setAdCode(adCodeMsgEntity.getAdCode());
        adCodeMsg.setProvince(adCodeMsgEntity.getProvince());
        adCodeMsg.setCity(adCodeMsgEntity.getCity());
        adCodeMsg.setArea(adCodeMsgEntity.getArea());
        adCodeMsg.setLevel(adCodeMsgEntity.getLevel());
        adCodeMsg.setGdId(adCodeMsgEntity.getGdId());
        adCodeMsg.setAddTime(adCodeMsgEntity.getAddTime());
        adCodeMsg.setUpdateTime(adCodeMsgEntity.getUpdateTime());
        adCodeMsg.setStatus(adCodeMsgEntity.getStatus());
        adCodeMsg.setFenceId(adCodeMsgEntity.getFenceId());
        adCodeMsg.setCustomAreaName(adCodeMsgEntity.getCustomAreaName());
        adCodeMsg.setAreaType(adCodeMsgEntity.getAreaType());
        adCodeMsg.setAreaDrawType(adCodeMsgEntity.getAreaDrawType());

        return adCodeMsg;

    }
}
