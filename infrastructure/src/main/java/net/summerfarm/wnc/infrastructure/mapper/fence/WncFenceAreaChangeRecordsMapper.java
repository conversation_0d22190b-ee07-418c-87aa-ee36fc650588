package net.summerfarm.wnc.infrastructure.mapper.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Mapper
public interface WncFenceAreaChangeRecordsMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncFenceAreaChangeRecords record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncFenceAreaChangeRecords record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncFenceAreaChangeRecords selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncFenceAreaChangeRecords> selectByCondition(WncFenceAreaChangeRecordsQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WncFenceAreaChangeRecordsEntity> getPage(WncFenceAreaChangeRecordsQueryParam param);

    /**
     * 批量更新围栏区域变更记录的围栏ID
     * @param areaIds 围栏区域变更记录ID集合
     * @param fenceId 围栏ID
     */
    void updateRecordsFenceId(@Param("ids") List<Long> areaIds,@Param("fenceId") Integer fenceId);

    /**
     * 批量更新围栏区域变更记录的
     * @param records 围栏区域变更记录
     */
    void batchUpdate(List<WncFenceAreaChangeRecords> records);
}

