package net.summerfarm.wnc.infrastructure.es.repository;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsCommandRepository;
import net.summerfarm.wnc.infrastructure.es.mapper.CustomFenceAreaEsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 自定义围栏区域
 * date: 2025/9/3 11:11<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class CustomFenceAreaEsCommandRepositoryImpl implements CustomFenceAreaEsCommandRepository {

    @Resource
    private CustomFenceAreaEsMapper customFenceAreaEsMapper;
}
