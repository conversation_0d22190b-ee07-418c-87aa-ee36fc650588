package net.summerfarm.wnc.domain.fence.repository;



import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceAreaChangeRecordsCommandRepository {

    WncFenceAreaChangeRecordsEntity insertSelective(WncFenceAreaChangeRecordsCommandParam param);

    int updateSelectiveById(WncFenceAreaChangeRecordsCommandParam param);

    int remove(Long id);

    /**
     * 批量更新围栏区域变更记录的围栏ID
     * @param areaIds 围栏区域变更记录ID集合
     * @param fenceId 围栏ID
     */
    void updateRecordsFenceId(List<Long> areaIds, Integer fenceId);

    /**
     * 批量更新
     * @param params 参数列表
     */
    void batchUpdate(List<WncFenceAreaChangeRecordsCommandParam> params);
}