package net.summerfarm.wnc.domain.warehouseMapping.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.query.WncWarehouseThirdMappingQueryParam;



/**
*
* <AUTHOR>
* @date 2025-06-11 15:08:31
* @version 1.0
*
*/
public interface WncWarehouseThirdMappingQueryRepository {

    PageInfo<WncWarehouseThirdMappingEntity> getPage(WncWarehouseThirdMappingQueryParam param);

    WncWarehouseThirdMappingEntity selectById(Long id);

    List<WncWarehouseThirdMappingEntity> selectByCondition(WncWarehouseThirdMappingQueryParam param);

}