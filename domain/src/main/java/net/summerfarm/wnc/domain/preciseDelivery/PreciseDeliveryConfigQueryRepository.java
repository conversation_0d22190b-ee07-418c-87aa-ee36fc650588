package net.summerfarm.wnc.domain.preciseDelivery;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.summerfarm.wnc.domain.preciseDelivery.entity.PreciseDeliveryConfigEntity;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigQueryParam;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigAreaValueObject;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;

import java.util.List;

/**
 * Description:精准送配置仓库查询接口
 * date: 2023/9/22 18:22
 *
 * <AUTHOR>
 */
public interface PreciseDeliveryConfigQueryRepository {

    /**
     * 查询详情 返回聚合所有数据
     * @param configId 配置ID
     * @return 结果
     */
    PreciseDeliveryConfigAggregate queryDetail(Long configId);

    /**
     * 查询详情 返回聚合根单表
     * @param configId 配置ID
     * @return 结果
     */
    PreciseDeliveryConfigEntity query(Long configId);

    /**
     * 分页查询
     * @param queryParam 查询参数
     * @return 结果
     */
    PageInfo<PreciseDeliveryConfigAggregate> queryPage(PreciseDeliveryConfigQueryParam queryParam);

    /**
     * 查询区域列表
     * @param queryParam 查询参数
     * @return 结果
     */
    List<PreciseDeliveryConfigAreaValueObject> queryAreaList(PreciseDeliveryConfigQueryParam queryParam);

    /**
     * 查询时效列表
     * @param city 城市
     * @param area 区域
     * @return 结果
     */
    List<PreciseDeliveryConfigTimeValueObject> queryTimeListByCityAndArea(String city, String area);
}
