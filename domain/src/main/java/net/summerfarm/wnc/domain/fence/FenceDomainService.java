package net.summerfarm.wnc.domain.fence;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseInventoryMappingQuery;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.param.query.FenceChannelBusinessWhiteConfigQueryParam;
import net.summerfarm.wnc.domain.fence.repository.FenceChannelBusinessWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.fence.service.FenceChannelBusinessWhiteConfigCommandDomainService;
import net.summerfarm.wnc.domain.warehouse.WarehouseInventoryMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseInventoryMappingEntity;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:围栏领域服务
 * date: 2023/11/29 18:44
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FenceDomainService {

    private final FenceRepository fenceRepository;
    private final WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
    private final WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;
    private final FenceChannelBusinessWhiteConfigQueryRepository fenceChannelBusinessWhiteConfigQueryRepository;
    private final AdCodeMsgRepository adCodeMsgRepository;
    private final FenceValidator fenceValidator;
    private final FenceChannelBusinessWhiteConfigCommandDomainService fenceChannelBusinessWhiteConfigCommandDomainService;

    public Integer queryFencePackId(FenceEntity fenceEntity) {
        if (fenceEntity == null){
            return null;
        }
        //查询相同运营区域的全部围栏
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder().areaNo(fenceEntity.getAreaNo()).build());

        if (CollectionUtils.isEmpty(fenceEntities)){
            //获取最大打包ID
            return fenceRepository.queryMaxPackId() + 1;
        }

        FenceEntity firstFenceWithSameArea = fenceEntities.get(0);
        //校验库存使用仓
        List<Integer> newStorageCenters = warehouseLogisticsMappingRepository.queryValidWarehouseNosByStoreNo(fenceEntity.getStoreNo());
        List<Integer> oldStorageCenters = warehouseLogisticsMappingRepository.queryValidWarehouseNosByStoreNo(firstFenceWithSameArea.getStoreNo());
        if(!Objects.equals(oldStorageCenters.size(),newStorageCenters.size()) || !oldStorageCenters.containsAll(newStorageCenters) ){
            throw new BizException("请调整配送仓的库存使用仓");
        }
        return firstFenceWithSameArea.getPackId();
    }

    public void createInventoryMapping(FenceEntity fenceEntity) {
        if (fenceEntity == null){
            return;
        }
        Long count = warehouseInventoryMappingRepository.queryCountByStoreNo(fenceEntity.getStoreNo());
        //判断该城配仓是否已有映射关系 有映射信息无需处理
        if (count != null && count > 0){
            return;
        }
        //无映射关系则为新城配仓 需根据围栏绑定运营服务区找到已有城配仓同步库存使用关系
        //判断该运营区域是否已使用 查询相同运营区域的全部围栏
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder().areaNo(fenceEntity.getAreaNo()).status(FenceEnums.Status.VALID.getValue()).build());
        //新运营区域 需要运营人员投放商品信息
        if (CollectionUtils.isEmpty(fenceEntities)){
            return;
        }
        FenceEntity firstFenceWithSameArea = fenceEntities.get(0);
        //老运营区域 获取该运营区域所覆盖有效状态围栏的首个已有配送仓
        Integer oldStoreNo = fenceRepository.queryValidStoreNoByPackId(firstFenceWithSameArea.getPackId()).get(0);
        //获取已有配送仓的库存使用关系
        List<WarehouseInventoryMappingEntity> oldWarehouseInventoryMappings = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder().storeNo(oldStoreNo).build());
        if (CollectionUtils.isEmpty(oldWarehouseInventoryMappings)){
            return;
        }
        List<WarehouseInventoryMappingEntity> needInsertMappings = oldWarehouseInventoryMappings.stream().map(e -> {
            WarehouseInventoryMappingEntity mapping = new WarehouseInventoryMappingEntity();
            mapping.setSku(e.getSku());
            mapping.setStoreNo(fenceEntity.getStoreNo());
            mapping.setWarehouseNo(e.getWarehouseNo());
            mapping.setCreateTime(LocalDateTime.now());
            return mapping;
        }).collect(Collectors.toList());
        warehouseInventoryMappingRepository.batchSave(needInsertMappings);
    }

    public FenceEntity queryDetail(Integer fenceId) {
        if (fenceId == null){
            return null;
        }
        FenceEntity fenceEntity = fenceRepository.queryDetail(fenceId);
        if (fenceEntity == null){
            throw new BizException("无效围栏信息");
        }
        if (Objects.equals(fenceEntity.getStatus(), FenceEnums.Status.INVALID)){
            //历史逻辑 无效状态置空区域信息
            fenceEntity.setAdCodeMsgEntities(Collections.emptyList());
        }
        return fenceEntity;
    }

    /**
     * 根据城配仓查询有效的围栏和有效的运营区域
     * @param storeNos 城配仓编号
     * @return 结果
     */
    public List<FenceEntity> queryValidFenceAreaByStoreNos(List<Integer> storeNos) {
        if(CollectionUtils.isEmpty(storeNos)){
            return Collections.emptyList();
        }
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder()
                .storeNos(storeNos)
                .status(FenceEnums.Status.VALID.getValue())
                .build());

        if(CollectionUtils.isEmpty(fenceEntities)){
            return Collections.emptyList();
        }
        List<Integer> areaNos = fenceEntities.stream().map(FenceEntity::getAreaNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(areaNos)){
            return Collections.emptyList();
        }

        return fenceEntities.stream().filter(fence -> areaNos.contains(fence.getAreaNo())).collect(Collectors.toList());
    }

    /**
     * 根据围栏ID集合查询围栏详情
     * @param fenceIds 围栏ID集合
     * @return 结果
     */
    public List<FenceEntity> queryFenceAllDetailList(List<Integer> fenceIds) {
        if (CollectionUtils.isEmpty(fenceIds)) {
            return Collections.emptyList();
        }

        // 围栏查询
        List<FenceEntity> fenceEntityList = fenceRepository.queryList(FenceQuery.builder().fenceIds(fenceIds).build());

        if (CollectionUtils.isEmpty(fenceEntityList)) {
            return Collections.emptyList();
        }

        // 查询围栏对应的城市区域信息
        List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgRepository.queryList(AdCodeMsgQuery.builder().fenceIds(fenceIds).build());

        // 围栏渠道白名单配置
        List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities = fenceChannelBusinessWhiteConfigQueryRepository.selectByCondition(FenceChannelBusinessWhiteConfigQueryParam.builder()
                .fenceIds(fenceIds)
                .build());

        // 按围栏ID分组AdCodeMsgEntity - 添加空值校验
        Map<Integer, List<AdCodeMsgEntity>> adCodeMsgMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(adCodeMsgEntities)) {
            adCodeMsgMap = adCodeMsgEntities.stream()
                    .filter(entity -> entity != null && entity.getFenceId() != null)
                    .collect(Collectors.groupingBy(AdCodeMsgEntity::getFenceId));
        }

        // 按围栏ID分组FenceChannelBusinessWhiteConfigEntity - 添加空值校验
        Map<Integer, List<FenceChannelBusinessWhiteConfigEntity>> fenceChannelConfigMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(fenceChannelBusinessWhiteConfigEntities)) {
            fenceChannelConfigMap = fenceChannelBusinessWhiteConfigEntities.stream()
                    .filter(entity -> entity != null && entity.getFenceId() != null)
                    .collect(Collectors.groupingBy(FenceChannelBusinessWhiteConfigEntity::getFenceId));
        }

        // 将相关数据聚合到围栏实体中
        for (FenceEntity fenceEntity : fenceEntityList) {
            // 校验围栏实体和围栏ID是否为空
            if (fenceEntity == null || fenceEntity.getId() == null) {
                continue;
            }

            Integer fenceId = fenceEntity.getId();

            // 设置区域编码信息 - 确保不为null
            List<AdCodeMsgEntity> adCodeMsgList = adCodeMsgMap.getOrDefault(fenceId, Collections.emptyList());
            fenceEntity.setAdCodeMsgEntities(adCodeMsgList != null ? adCodeMsgList : Collections.emptyList());

            // 设置渠道业务白名单配置 - 确保不为null
            List<FenceChannelBusinessWhiteConfigEntity> channelConfigList = fenceChannelConfigMap.getOrDefault(fenceId, Collections.emptyList());
            fenceEntity.setFenceChannelBusinessWhiteConfigEntities(channelConfigList != null ? channelConfigList : Collections.emptyList());
        }

        return fenceEntityList;
    }

    /**
     * 创建围栏
     * @param fenceEntity 围栏实体
     */
    public void createFence(FenceEntity fenceEntity) {
        if(fenceEntity.getType() == FenceEnums.Type.CUSTOM){
            fenceValidator.validateCustomFence(fenceEntity);
        }else{
            fenceValidator.validateFence(fenceEntity);
        }
        //排序配送周期字段
        fenceEntity.getFenceDeliveryEntity().resortDeliveryFrequent();
        //获取打包编号
        Integer packId = this.queryFencePackId(fenceEntity);
        fenceEntity.setPackId(packId);
        //处理库存映射
        this.createInventoryMapping(fenceEntity);
        //获取操作人信息
        UserBase user = UserInfoHolder.getUser();
        fenceEntity.create(user != null ? user.getBizUserId() : null);
        fenceRepository.saveOrUpdate(fenceEntity);
        List<FenceChannelBusinessWhiteConfigEntity> businessWhiteConfigEntities = fenceEntity.getFenceChannelBusinessWhiteConfigEntities();
        if(!CollectionUtils.isEmpty(businessWhiteConfigEntities)){
            businessWhiteConfigEntities.forEach(e -> e.setFenceId(fenceEntity.getId()));
            fenceChannelBusinessWhiteConfigCommandDomainService.fenceBatchSaveOrUpdate(fenceEntity.getId(),businessWhiteConfigEntities);
        }
    }

    /**
     * 围栏的区域解绑并失效
     * @param fenceIds 围栏ID集合
     */
    public void unbindAndInvalidArea(List<Integer> fenceIds) {
        if (CollectionUtils.isEmpty(fenceIds)) {
            return;
        }
        adCodeMsgRepository.unbindAndInvalidAreaByFenceIds(fenceIds, AdCodeMsgEnums.Status.INVALID.getValue());
    }

    /**
     * 创建区域并绑定围栏
     * @param adCodeMsgDetailList 区域实体集合
     */
    public void createAreaBindingFence(List<AdCodeMsgEntity> adCodeMsgDetailList) {
        if (CollectionUtils.isEmpty(adCodeMsgDetailList) ) {
            return;
        }

        adCodeMsgRepository.save(adCodeMsgDetailList);
    }
}
