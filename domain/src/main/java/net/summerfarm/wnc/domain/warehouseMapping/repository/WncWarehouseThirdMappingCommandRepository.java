package net.summerfarm.wnc.domain.warehouseMapping.repository;



import net.summerfarm.wnc.domain.warehouseMapping.entity.WncWarehouseThirdMappingEntity;
import net.summerfarm.wnc.domain.warehouseMapping.param.command.WncWarehouseThirdMappingCommandParam;




/**
*
* <AUTHOR>
* @date 2025-06-11 15:08:31
* @version 1.0
*
*/
public interface WncWarehouseThirdMappingCommandRepository {

    WncWarehouseThirdMappingEntity insertSelective(WncWarehouseThirdMappingCommandParam param);

    int updateSelectiveById(WncWarehouseThirdMappingCommandParam param);

    int remove(Long id);

}