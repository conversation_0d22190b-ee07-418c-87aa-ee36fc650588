package net.summerfarm.wnc.domain.fence.service;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 *
 * @Title: 城市区域切仓记录领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Slf4j
@Service
public class WncCityAreaChangeWarehouseRecordsCommandDomainService {


    @Autowired
    private WncCityAreaChangeWarehouseRecordsCommandRepository wncCityAreaChangeWarehouseRecordsCommandRepository;
    @Autowired
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Autowired
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Autowired
    private WncFenceChangeRecordsCommandRepository wncFenceChangeRecordsCommandRepository;


    public WncCityAreaChangeWarehouseRecordsEntity insert(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.insertSelective(param);
    }


    public int update(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.remove(id);
    }

    /**
     * 预约切仓时间
     * @param cityAreaChangeWarehouseRecordId 城市区域变更记录ID
     * @param preExeTime 切仓时间
     * @param fenceChangeTaskId 切仓任务ID
     */
    public void preExeTime(Long cityAreaChangeWarehouseRecordId, LocalDateTime preExeTime, Long fenceChangeTaskId) {
        if (cityAreaChangeWarehouseRecordId == null || preExeTime == null || fenceChangeTaskId == null) {
            return;
        }
        WncCityAreaChangeWarehouseRecordsEntity cityAreaChangeWarehouseRecordsEntity = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(cityAreaChangeWarehouseRecordId);
        if(cityAreaChangeWarehouseRecordsEntity == null){
            throw new BizException("城市区域切仓记录不存在，预约切仓时间失败");
        }
        if (!Objects.equals(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue(), cityAreaChangeWarehouseRecordsEntity.getChangeStatus())) {
            throw new BizException("非待生效状态，不可预约切仓时间");
        }
        // 更新城市区域切仓记录
        WncCityAreaChangeWarehouseRecordsCommandParam commandParam = new WncCityAreaChangeWarehouseRecordsCommandParam();
        commandParam.setId(cityAreaChangeWarehouseRecordId);
        commandParam.setPreExeTime(preExeTime);
        commandParam.setFenceChangeTaskId(fenceChangeTaskId);
        wncCityAreaChangeWarehouseRecordsCommandRepository.updateSelectiveById(commandParam);

        String changeBatchNo = cityAreaChangeWarehouseRecordsEntity.getChangeBatchNo();

        // 更新围栏切仓记录
        WncFenceChangeRecordsQueryParam fenceChangeRecordsQueryParam = new WncFenceChangeRecordsQueryParam();
        fenceChangeRecordsQueryParam.setChangeBatchNo(changeBatchNo);
        List<WncFenceChangeRecordsEntity> changeBatchNoFenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectByCondition(fenceChangeRecordsQueryParam);
        if (CollectionUtils.isEmpty(changeBatchNoFenceChangeRecords)) {
            log.info("变更围栏数据为空");
            return;
        }
        wncFenceChangeRecordsCommandRepository.updatePreExeTimeAndFenceChangeTaskIdByChangeBatchNo(preExeTime, fenceChangeTaskId, changeBatchNo);
    }

    public void cancelFenceChangeTask(Long fenceChangeTaskId) {
        // 根据切仓任务找出城市区域切仓记录
        WncCityAreaChangeWarehouseRecordsQueryParam queryParam = new WncCityAreaChangeWarehouseRecordsQueryParam();
        queryParam.setFenceChangeTaskId(fenceChangeTaskId);
        List<WncCityAreaChangeWarehouseRecordsEntity> cityAreaChangeWarehouseRecords = wncCityAreaChangeWarehouseRecordsQueryRepository.selectByCondition(queryParam);

        if (CollectionUtils.isEmpty(cityAreaChangeWarehouseRecords)){
            log.info("区域切仓记录为空无需处理,fenceChangeTaskId:{}",fenceChangeTaskId);
            return;
        }

        cityAreaChangeWarehouseRecords.forEach(e -> {
            if(!Objects.equals(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue(), e.getChangeStatus())){
                throw new BizException("城市区域切仓记录状态非等待生效中，不可取消");
            }
        });


        if (Objects.equals(cityAreaChangeWarehouseRecords.get(0).getAreaDefinationType(), WncCityAreaChangeWarehouseRecordsEnums.AreaDefinationType.NORMAL.getValue())) {
            // 普通自定义区域，取消切仓任务把城市区域切仓记录状态变为已取消
            wncCityAreaChangeWarehouseRecordsCommandRepository.updateChangeStatusByFenceChangeTaskId(fenceChangeTaskId, WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.CANCEL.getValue());
        }else{

            // 重置自定义区域的切仓任务，预约切仓时间置空、切仓任务ID置空、状态置为待生效
            wncCityAreaChangeWarehouseRecordsCommandRepository.resetCustomAreaFenceChangeTask(fenceChangeTaskId);

            // 置空围栏变更记录上的切仓ID
            wncFenceChangeRecordsCommandRepository.cancelFenceChangeTask(fenceChangeTaskId);
        }
    }

    public void batchInsert(List<WncCityAreaChangeWarehouseRecordsCommandParam> cityAreaChangeWarehouseRecordsList) {
        if(CollectionUtils.isEmpty(cityAreaChangeWarehouseRecordsList)){
            return;
        }
        wncCityAreaChangeWarehouseRecordsCommandRepository.batchInsert(cityAreaChangeWarehouseRecordsList);
    }
}
