package net.summerfarm.wnc.domain.preciseDelivery;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.TimeInterval;
import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.domain.preciseDelivery.aggregate.PreciseDeliveryConfigAggregate;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigAreaCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigQueryParam;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigTimeCommandParam;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigAreaValueObject;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:精准送配置校验器
 * date: 2024/1/23 15:14
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreciseDeliveryConfigValidator {

    private final PreciseDeliveryConfigQueryRepository preciseDeliveryConfigQueryRepository;

    public void validateConfig(PreciseDeliveryConfigCommandParam param) {
        if (param == null){
            return;
        }
        List<PreciseDeliveryConfigAreaCommandParam> areaList = param.getAreaList();
        List<PreciseDeliveryConfigTimeCommandParam> timeList = param.getTimeList();
        //校验所选行政区是否跨市
        boolean cityMatch = areaList.stream().allMatch(e -> Objects.equals(e.getCity(), param.getCity()));
        if (!cityMatch){
            throw new BizException("所选行政区域不能跨市");
        }
        //校验所选行政区是否已配置
        List<PreciseDeliveryConfigAreaValueObject> existAreaList = preciseDeliveryConfigQueryRepository.queryAreaList(PreciseDeliveryConfigQueryParam.builder().city(param.getCity()).build());
        //根据配置ID进行过滤 编辑时需要过滤掉已存在的区域
        Map<String, PreciseDeliveryConfigAreaValueObject> existAreaMap = existAreaList.stream().filter(e -> !Objects.equals(e.getConfigId(), param.getConfigId()))
                .collect(Collectors.toMap(PreciseDeliveryConfigAreaValueObject::buildUk, Function.identity(), (oldData, newData) -> newData));
        for (PreciseDeliveryConfigAreaCommandParam commandParam : areaList) {
            PreciseDeliveryConfigAreaValueObject areaValueObject = existAreaMap.get(commandParam.buildUk());
            if (areaValueObject != null){
                throw new BizException(String.format("[%s %s]已存在配置，请勿重复添加", areaValueObject.getCity(), areaValueObject.getArea()));
            }
        }
        //校验所选时效是否有重叠
        boolean timeMatch = timeList.stream().allMatch(PreciseDeliveryConfigTimeCommandParam::isEndAfterBegin);
        if (!timeMatch){
            throw new BizException("所选时效配置不满足[单段结束时间大于开始时间]");
        }
        if (checkTimeOverlaps(timeList)){
            throw new BizException("所选时效配置不满足[下一段开始时间大于等于上一段结束时间]");
        }
    }

    /**
     * 检查配置时间段是否有重叠
     * @param intervals 时间段集合
     * @return 是否有重叠
     */
    public static boolean checkTimeOverlaps(List<PreciseDeliveryConfigTimeCommandParam> intervals) {
        if (CollectionUtil.isEmpty(intervals)){
            return false;
        }
        //根据开始时间进行排序
        intervals = intervals.stream().sorted(Comparator.comparing(PreciseDeliveryConfigTimeCommandParam::getBeginTime)).collect(Collectors.toList());
        for (int i = 0; i < intervals.size(); i++) {
            PreciseDeliveryConfigTimeCommandParam curTime = intervals.get(i);
            if (i == 0){
                continue;
            }
            PreciseDeliveryConfigTimeCommandParam prevTime = intervals.get(i - 1);
            if (curTime.getBeginTime().isBefore(prevTime.getEndTime())){
                return true;
            }
        }
        return false;
    }
}
