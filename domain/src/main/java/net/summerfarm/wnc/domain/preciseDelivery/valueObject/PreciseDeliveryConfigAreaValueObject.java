package net.summerfarm.wnc.domain.preciseDelivery.valueObject;

import lombok.Data;
import net.summerfarm.wnc.common.constants.AppConsts;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:精准送配置区域值对象
 * date: 2024/1/22 14:59
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigAreaValueObject implements Serializable {

    private static final long serialVersionUID = 4349469750975016870L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public String buildUk(){
        return this.city + AppConsts.Symbol.HASH_TAG + this.area;
    }
}
