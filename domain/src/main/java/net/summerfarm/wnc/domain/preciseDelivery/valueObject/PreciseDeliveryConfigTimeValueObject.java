package net.summerfarm.wnc.domain.preciseDelivery.valueObject;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Description:精准送配置时效值对象
 * date: 2024/1/22 15:05
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigTimeValueObject implements Serializable {

    private static final long serialVersionUID = 6841049161374800758L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 开始时间
     */
    private LocalTime beginTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
