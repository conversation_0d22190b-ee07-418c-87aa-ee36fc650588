package net.summerfarm.wnc.domain.preciseDelivery;

import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;

/**
 * Description:精准送配置仓库操作接口
 * date: 2023/9/22 18:22
 *
 * <AUTHOR>
 */
public interface PreciseDeliveryConfigCommandRepository {


    /**
     * 保存精准送配置
     * @param preciseDeliveryConfigCommand 精准送配置参数
     */
    void save(PreciseDeliveryConfigCommandParam preciseDeliveryConfigCommand);

    /**
     * 删除联系人配置
     * @param configId 主键ID
     */
    void remove(Long configId);

    /**
     * 更新精准送配置
     * @param preciseDeliveryConfigCommand 精准送配置参数
     */
    void update(PreciseDeliveryConfigCommandParam preciseDeliveryConfigCommand);
}
