package net.summerfarm.wnc.domain.fence.service;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 *
 * @Title: 围栏变更执行记录表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Slf4j
@Service
public class WncFenceChangeRecordsCommandDomainService {


    @Autowired
    private WncFenceChangeRecordsCommandRepository wncFenceChangeRecordsCommandRepository;
    @Autowired
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Autowired
    private WncFenceAreaChangeRecordsCommandDomainService wncFenceAreaChangeRecordsCommandDomainService;
    @Autowired
    private WncFenceAreaChangeRecordsCommandRepository wncFenceAreaChangeRecordsCommandRepository;



    public WncFenceChangeRecordsEntity insert(WncFenceChangeRecordsCommandParam param) {
        return wncFenceChangeRecordsCommandRepository.insertSelective(param);
    }


    public int update(WncFenceChangeRecordsCommandParam param) {
        return wncFenceChangeRecordsCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wncFenceChangeRecordsCommandRepository.remove(id);
    }

    /**
     * 创建围栏区域记录
     *
     * @param fenceChangeRecords 围栏变更记录列表
     */
    public void createFenceAreaRecord(List<WncFenceChangeRecordsCommandParam> fenceChangeRecords) {

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return;
        }

        for (WncFenceChangeRecordsCommandParam fenceChangeRecord : fenceChangeRecords) {
            WncFenceChangeRecordsEntity savedEntity = wncFenceChangeRecordsCommandRepository.insertSelective(fenceChangeRecord);

            List<WncFenceAreaChangeRecordsCommandParam> fenceAreaChangeRecords = fenceChangeRecord.getFenceAreaChangeRecords();
            fenceAreaChangeRecords.forEach(areaRecord -> {
                areaRecord.setFenceChangeId(savedEntity.getId());
                wncFenceAreaChangeRecordsCommandRepository.insertSelective(areaRecord);
            });
        }

    }

    /**
     * 创建历史围栏区域记录（批量优化版本）
     * 使用批量插入提升性能，但无法获取生成的ID进行关联
     * 适用于不需要ID关联的场景
     *
     * @param fenceChangeRecords 围栏变更记录列表
     * @param fenceAreaChangeRecords 围栏区域变更记录列表（需要预先设置好fenceChangeId）
     */
    public void createHistoryFenceAreaRecordBatch(List<WncFenceChangeRecordsCommandParam> fenceChangeRecords,
                                                 List<WncFenceAreaChangeRecordsCommandParam> fenceAreaChangeRecords) {

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return;
        }

        // 1. 批量插入围栏变更记录（性能最优，但无法获取生成的ID）
        int insertedCount = wncFenceChangeRecordsCommandRepository.batchInsert(fenceChangeRecords);
        log.info("批量插入围栏变更记录完成，插入数量: {}", insertedCount);

        // 2. 批量插入围栏区域变更记录（需要预先设置好fenceChangeId）
        if (!CollectionUtils.isEmpty(fenceAreaChangeRecords)) {
            for (WncFenceAreaChangeRecordsCommandParam areaRecord : fenceAreaChangeRecords) {
                wncFenceAreaChangeRecordsCommandRepository.insertSelective(areaRecord);
            }
            log.info("批量插入围栏区域变更记录完成，插入数量: {}", fenceAreaChangeRecords.size());
        }
    }

    /**
     * 执行围栏切仓区域处理任务
     */
    public void executeFenceChangeAreaHandle(String changeBatchNo) {
        if (changeBatchNo == null) {
            return;
        }

        // 根据记录信息创建或者更新围栏、区域数据
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo);
        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return;
        }

        // 找出围栏、区域没有围栏ID数据，进行创建

    }

}
