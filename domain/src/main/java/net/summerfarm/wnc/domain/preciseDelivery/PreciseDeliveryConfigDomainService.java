package net.summerfarm.wnc.domain.preciseDelivery;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.domain.preciseDelivery.param.PreciseDeliveryConfigCommandParam;
import org.springframework.stereotype.Component;

/**
 * Description:精准送配置领域服务
 * date: 2024/1/23 15:14
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreciseDeliveryConfigDomainService {

    private final PreciseDeliveryConfigCommandRepository preciseDeliveryConfigCommandRepository;
    public void saveConfig(PreciseDeliveryConfigCommandParam param) {
        if (param == null){
            return;
        }
        preciseDeliveryConfigCommandRepository.save(param);
    }

    public void editConfig(PreciseDeliveryConfigCommandParam param) {
        if (param == null){
            return;
        }
        preciseDeliveryConfigCommandRepository.update(param);
    }

    public void removeConfig(Long configId) {
        if (configId == null){
            return;
        }
        preciseDeliveryConfigCommandRepository.remove(configId);
    }
}
