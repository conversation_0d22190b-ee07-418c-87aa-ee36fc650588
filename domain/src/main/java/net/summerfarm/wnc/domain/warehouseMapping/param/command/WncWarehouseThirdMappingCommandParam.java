package net.summerfarm.wnc.domain.warehouseMapping.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-06-11 15:08:31
 * @version 1.0
 *
 */
@Data
public class WncWarehouseThirdMappingCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 仓库编号
	 */
	private Integer warehouseNo;

	/**
	 * 三方来源名称
	 */
	private String thirdSourceName;

	/**
	 * 三方仓库编号
	 */
	private String thirdWarehouseNo;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 开放平台appkey
	 */
	private String openPlatformAppKey;

	

	
}